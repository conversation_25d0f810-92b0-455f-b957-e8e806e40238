/* ====================================
   WaiterCartScreen Component Styles
   ==================================== */

/* استيراد المتغيرات المميزة لشاشة الطلبات */
@import '../variables/orders-variables.css';

/* متغيرات CSS خاصة بشاشة السلة */


/* Header شاشة السلة */
.waiter-cart-screen .screen-header {
  background: linear-gradient(135deg, var(--menu-primary-color), var(--menu-secondary-color));
  padding: var(--orders-spacing-lg);
  border-radius: var(--orders-border-radius);
  margin-bottom: var(--orders-spacing-lg);
  color: white;
  box-shadow: var(--orders-shadow);
}

.waiter-cart-screen .screen-title {
  font-size: 2rem;
  font-weight: 800;
  margin: 0 0 var(--orders-spacing-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--orders-spacing-sm);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.waiter-cart-screen .screen-subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 1.05rem;
  font-weight: 500;
}

/* حالة السلة الفارغة */
.waiter-cart-screen .empty-cart-state {
  text-align: center;
  padding: var(--orders-spacing-xl);
  background: var(--orders-bg-primary);
  border-radius: var(--orders-border-radius);
  box-shadow: var(--orders-shadow);
  border: 1px solid var(--orders-border-color);
}

.waiter-cart-screen .empty-cart-icon {
  width: 100px;
  height: 100px;
  margin: 0 auto var(--orders-spacing-lg);
  background: linear-gradient(135deg, var(--menu-primary-color), var(--menu-secondary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.waiter-cart-screen .empty-cart-state h3 {
  color: var(--orders-text-primary);
  margin-bottom: var(--orders-spacing-sm);
  font-size: 1.8rem;
  font-weight: 700;
}

.waiter-cart-screen .empty-cart-state p {
  color: var(--orders-text-secondary);
  margin-bottom: var(--orders-spacing-lg);
  font-size: 1.1rem;
  line-height: 1.5;
}

.waiter-cart-screen .browse-menu-btn {
  background: linear-gradient(135deg, var(--menu-primary-color), var(--menu-secondary-color));
  color: white;
  border: none;
  padding: var(--orders-spacing-md) var(--orders-spacing-xl);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 700;
  font-size: 1.1rem;
  display: inline-flex;
  align-items: center;
  gap: var(--orders-spacing-sm);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.waiter-cart-screen .browse-menu-btn:hover {
  background: linear-gradient(135deg, var(--menu-secondary-color), var(--menu-primary-color));
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

/* محتوى السلة */
.waiter-cart-screen .cart-content {
  display: flex;
  flex-direction: column;
  gap: var(--orders-spacing-lg);
}

/* عناصر السلة */
.waiter-cart-screen .cart-items {
  background: var(--orders-bg-primary);
  border-radius: var(--orders-border-radius);
  box-shadow: var(--orders-shadow);
  border: 1px solid var(--orders-border-color);
  overflow: hidden;
}

.waiter-cart-screen .cart-item {
  display: flex;
  align-items: flex-start;
  gap: var(--orders-spacing-md);
  padding: var(--orders-spacing-md);
  border-bottom: 1px solid var(--orders-border-color);
  transition: all 0.3s ease;
}

.waiter-cart-screen .cart-item:last-child {
  border-bottom: none;
}

.waiter-cart-screen .cart-item:hover {
  background: var(--orders-bg-secondary);
}

.waiter-cart-screen .cart-item-info {
  flex: 1;
  min-width: 0;
}

.waiter-cart-screen .cart-item-info h4 {
  margin: 0 0 var(--orders-spacing-xs) 0;
  color: var(--orders-text-primary);
  font-size: 1.2rem;
  font-weight: 700;
  line-height: 1.3;
}

.waiter-cart-screen .cart-item-price {
  color: var(--menu-primary-color);
  font-weight: 700;
  font-size: 1.1rem;
  margin-bottom: var(--orders-spacing-sm);
}

/* ملاحظات العنصر */
.waiter-cart-screen .cart-item-notes {
  margin-top: var(--orders-spacing-sm);
}

.waiter-cart-screen .notes-label {
  display: flex;
  align-items: center;
  gap: var(--orders-spacing-xs);
  color: var(--orders-text-secondary);
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: var(--orders-spacing-xs);
}

.waiter-cart-screen .notes-input {
  width: 100%;
  padding: var(--orders-spacing-sm);
  border: 2px solid var(--orders-border-color);
  border-radius: 8px;
  font-size: 0.95rem;
  resize: vertical;
  min-height: 50px;
  transition: all 0.3s ease;
  text-align: right;
  font-family: inherit;
  background: var(--orders-bg-secondary);
}

.waiter-cart-screen .notes-input:focus {
  outline: none;
  border-color: var(--menu-primary-color);
  box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.1);
  background: white;
}

.waiter-cart-screen .notes-input::placeholder {
  color: var(--orders-text-muted);
}

/* التحكم في الكمية */
.waiter-cart-screen .cart-item-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--orders-spacing-md);
  min-width: 120px; /* Base minimum width for all browsers */
  flex-shrink: 0; /* Prevent shrinking below content size */
}

.waiter-cart-screen .quantity-controls {
  display: flex;
  align-items: center;
  gap: var(--orders-spacing-sm);
  background: var(--orders-bg-secondary);
  border-radius: var(--orders-border-radius);
  padding: var(--orders-spacing-xs);
  border: 1px solid var(--orders-border-color);
}

.waiter-cart-screen .quantity-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: linear-gradient(135deg, var(--menu-primary-color), var(--menu-secondary-color));
  color: white;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.waiter-cart-screen .quantity-btn:hover {
  background: linear-gradient(135deg, var(--menu-secondary-color), var(--menu-primary-color));
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.waiter-cart-screen .quantity-btn:active {
  transform: scale(0.95);
}

.waiter-cart-screen .quantity {
  min-width: 45px;
  text-align: center;
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--menu-primary-color);
  background: white;
  padding: var(--orders-spacing-xs);
  border-radius: 6px;
  border: 2px solid var(--menu-border-color);
}

.waiter-cart-screen .remove-item-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: linear-gradient(135deg, var(--orders-danger-color), #c82333);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.waiter-cart-screen .remove-item-btn:hover {
  background: linear-gradient(135deg, #c82333, #a71e2a);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

/* نموذج السلة */
.waiter-cart-screen .cart-form {
  background: var(--orders-bg-primary);
  padding: var(--orders-spacing-lg);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 2px solid var(--orders-border-color);
}

.waiter-cart-screen .form-group {
  margin-bottom: var(--orders-spacing-md);
}

.waiter-cart-screen .form-group:last-child {
  margin-bottom: 0;
}

.waiter-cart-screen .form-group label {
  display: block;
  color: var(--orders-text-primary);
  font-weight: 700;
  margin-bottom: var(--orders-spacing-sm);
  font-size: 1.1rem;
}

.waiter-cart-screen .form-group input {
  width: 100%;
  padding: var(--orders-spacing-md);
  border: 2px solid var(--orders-border-color);
  border-radius: 8px;
  font-size: 1.05rem;
  transition: all 0.3s ease;
  text-align: right;
  background: var(--orders-bg-secondary);
}

.waiter-cart-screen .form-group input:focus {
  outline: none;
  border-color: var(--menu-primary-color);
  box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.1);
  background: white;
}

.waiter-cart-screen .form-group input::placeholder {
  color: var(--orders-text-muted);
}

/* إجمالي السلة */
.waiter-cart-screen .cart-total {
  background: linear-gradient(135deg, var(--menu-primary-color), var(--menu-secondary-color));
  color: white;
  padding: var(--orders-spacing-xl);
  border-radius: 12px;
  text-align: center;
  font-size: 1.5rem;
  font-weight: 800;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* أزرار العمليات */
.waiter-cart-screen .cart-actions {
  display: flex;
  gap: var(--orders-spacing-md);
  justify-content: space-between;
}

.waiter-cart-screen .clear-cart-btn {
  background: transparent;
  color: var(--orders-danger-color);
  border: 2px solid var(--orders-danger-color);
  padding: var(--orders-spacing-md) var(--orders-spacing-lg);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 700;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: var(--orders-spacing-sm);
  flex: 0 0 auto;
}

.waiter-cart-screen .clear-cart-btn:hover {
  background: var(--orders-danger-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.waiter-cart-screen .submit-order-btn {
  background: linear-gradient(135deg, var(--orders-success-color), #20c997);
  color: white;
  border: none;
  padding: var(--orders-spacing-lg) var(--orders-spacing-xl);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 700;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: var(--orders-spacing-sm);
  flex: 1;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.waiter-cart-screen .submit-order-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #20c997, #17a2b8);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.waiter-cart-screen .submit-order-btn:disabled {
  background: var(--orders-text-muted);
  cursor: not-allowed;
  opacity: 0.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .waiter-cart-screen .cart-item {
    flex-direction: column;
    align-items: stretch;
  }
  
  .waiter-cart-screen .cart-item-controls {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--orders-spacing-md);
  }
  
  .waiter-cart-screen .cart-actions {
    flex-direction: column;
  }
  
  .waiter-cart-screen .clear-cart-btn,
  .waiter-cart-screen .submit-order-btn {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .waiter-cart-screen .screen-header {
    padding: var(--orders-spacing-md);
  }

  .waiter-cart-screen .screen-title {
    font-size: 1.6rem;
  }

  .waiter-cart-screen .screen-subtitle {
    font-size: 1rem;
  }

  .waiter-cart-screen .cart-item {
    padding: var(--orders-spacing-sm);
  }

  .waiter-cart-screen .cart-item-info h4 {
    font-size: 1.1rem;
  }

  .waiter-cart-screen .cart-item-price {
    font-size: 1rem;
  }

  .waiter-cart-screen .cart-form {
    padding: var(--orders-spacing-md);
  }

  .waiter-cart-screen .cart-total {
    padding: var(--orders-spacing-lg);
    font-size: 1.3rem;
  }

  .waiter-cart-screen .empty-cart-state {
    padding: var(--orders-spacing-lg);
  }

  .waiter-cart-screen .empty-cart-state h3 {
    font-size: 1.5rem;
  }

  .waiter-cart-screen .empty-cart-state p {
    font-size: 1rem;
  }

  .waiter-cart-screen .empty-cart-icon {
    width: 80px;
    height: 80px;
    font-size: 2rem;
  }

  .waiter-cart-screen .quantity-controls {
    gap: var(--orders-spacing-xs);
  }

  .waiter-cart-screen .quantity-btn,
  .waiter-cart-screen .remove-item-btn {
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
  }

  .waiter-cart-screen .quantity {
    min-width: 35px;
    font-size: 1.1rem;
  }

  .waiter-cart-screen .submit-order-btn {
    font-size: 1.1rem;
    padding: var(--orders-spacing-md) var(--orders-spacing-lg);
  }
}

