/* Orders Screen Variables - متغيرات شاشة الطلبات */
:root {
  /* ألوان شاشة الطلبات */
  --orders-primary-color: #2c3e50;
  --orders-secondary-color: #3498db;
  --orders-accent-color: #34495e;
  --orders-bg-primary: #ffffff;
  --orders-bg-secondary: #f8f9fa;
  --orders-text-primary: #000000;
  --orders-text-secondary: #6c757d;
  --orders-border-color: #dee2e6;
  --orders-border-light: #f1f3f4;
  
  /* ألوان الحالة لشاشة الطلبات */
  --orders-success-color: #27ae60;
  --orders-success-light: rgba(39, 174, 96, 0.1);
  --orders-warning-color: #f39c12;
  --orders-warning-light: rgba(243, 156, 18, 0.1);
  --orders-error-color: #e74c3c;
  --orders-error-light: rgba(231, 76, 60, 0.1);
  --orders-info-color: #17a2b8;
  --orders-info-light: rgba(23, 162, 184, 0.1);
  
  /* متغيرات الخط لشاشة الطلبات */
  --orders-font-size-xs: 12px;
  --orders-font-size-sm: 14px;
  --orders-font-size-md: 16px;
  --orders-font-size-lg: 18px;
  --orders-font-size-xl: 20px;
  --orders-font-size-xxl: 24px;
  
  /* متغيرات المسافة لشاشة الطلبات */
  --orders-spacing-xs: 0.25rem;
  --orders-spacing-sm: 0.5rem;
  --orders-spacing-md: 1rem;
  --orders-spacing-lg: 1.5rem;
  --orders-spacing-xl: 2rem;
  --orders-spacing-xxl: 3rem;
  
  /* متغيرات الحدود لشاشة الطلبات */
  --orders-border-radius: 8px;
  --orders-border-radius-sm: 4px;
  --orders-border-radius-lg: 12px;
  --orders-border-radius-xl: 16px;
  
  /* متغيرات الظلال لشاشة الطلبات */
  --orders-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --orders-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
  --orders-shadow-md: 0 2px 8px rgba(0, 0, 0, 0.1);
  --orders-shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.15);
  
  /* متغيرات الانتقال لشاشة الطلبات */
  --orders-transition-fast: 0.2s ease;
  --orders-transition-medium: 0.3s ease;
  --orders-transition-slow: 0.5s ease;
  
  /* متغيرات خاصة بشاشة الطلبات */
  --orders-card-min-width: 350px;
  --orders-grid-gap: 1.5rem;
  --orders-primary-hover: #34495e;
  --orders-primary-light: rgba(44, 62, 80, 0.1);
  --orders-pending-border: var(--orders-warning-color);
  --orders-preparing-border: var(--orders-info-color);
  --orders-ready-border: var(--orders-success-color);
  --orders-completed-border: var(--orders-primary-color);
}



