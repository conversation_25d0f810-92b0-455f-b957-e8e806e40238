/* مكون زر الطلب الصوتي */
.voice-order-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--menu-spacing-sm);
  margin: var(--menu-spacing-md) 0;
}

.voice-order-btn {
  position: relative;
  background: linear-gradient(135deg, var(--menu-primary-color), var(--menu-secondary-color));
  color: white;
  border: none;
  border-radius: 16px;
  padding: var(--menu-spacing-lg) var(--menu-spacing-xl);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  font-weight: 700;
  font-size: 1.1rem;
  box-shadow: 0 4px 20px rgba(44, 62, 80, 0.3);
  overflow: hidden;
  min-width: 200px;
  text-align: center;
}

.voice-order-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--menu-secondary-color), var(--menu-primary-color));
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(44, 62, 80, 0.4);
}

.voice-order-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* حالة الاستماع */
.voice-order-btn.listening {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  animation: pulse-listening 1.5s infinite;
}

.voice-order-btn.listening:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
}

/* حالة المعالجة */
.voice-order-btn.processing {
  background: linear-gradient(135deg, var(--menu-warning-color), #e67e22);
}

.voice-btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--menu-spacing-sm);
  position: relative;
  z-index: 2;
}

.voice-icon {
  font-size: 1.3rem;
  transition: all 0.3s ease;
}

.voice-order-btn.listening .voice-icon {
  animation: bounce 0.6s infinite alternate;
}

.voice-order-btn.processing .voice-icon {
  animation: spin 1s linear infinite;
}

.voice-btn-text {
  font-weight: 700;
  letter-spacing: 0.5px;
}

/* أنيميشن الموجات الصوتية */
.voice-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  gap: 3px;
  z-index: 1;
}

.wave {
  width: 3px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 2px;
  animation: wave-animation 1.2s infinite ease-in-out;
}

.wave1 {
  height: 20px;
  animation-delay: 0s;
}

.wave2 {
  height: 30px;
  animation-delay: 0.2s;
}

.wave3 {
  height: 25px;
  animation-delay: 0.4s;
}

/* النص الأخير المنطوق */
.last-transcript {
  background: rgba(44, 62, 80, 0.1);
  padding: var(--menu-spacing-xs) var(--menu-spacing-sm);
  border-radius: 8px;
  border: 1px solid rgba(44, 62, 80, 0.2);
  max-width: 300px;
  text-align: center;
}

.last-transcript small {
  color: var(--menu-text-secondary);
  font-size: 0.85rem;
  line-height: 1.3;
  display: block;
  word-break: break-word;
}

/* نص المساعدة */
.voice-help {
  text-align: center;
  opacity: 0.8;
}

.voice-help small {
  color: var(--menu-text-secondary);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--menu-spacing-xs);
}

.voice-help i {
  color: var(--menu-info-color);
}

/* الأنيميشنز */
@keyframes pulse-listening {
  0% {
    box-shadow: 0 4px 20px rgba(231, 76, 60, 0.3);
  }
  50% {
    box-shadow: 0 4px 30px rgba(231, 76, 60, 0.6);
  }
  100% {
    box-shadow: 0 4px 20px rgba(231, 76, 60, 0.3);
  }
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-3px);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes wave-animation {
  0%, 40%, 100% {
    transform: scaleY(0.4);
    opacity: 0.5;
  }
  20% {
    transform: scaleY(1);
    opacity: 1;
  }
}

/* الاستجابة للشاشات المختلفة */
@media (max-width: 768px) {
  .voice-order-btn {
    min-width: 180px;
    padding: var(--menu-spacing-md) var(--menu-spacing-lg);
    font-size: 1rem;
  }
  
  .voice-icon {
    font-size: 1.2rem;
  }
  
  .last-transcript {
    max-width: 250px;
  }
}

@media (max-width: 480px) {
  .voice-order-btn {
    min-width: 160px;
    padding: var(--menu-spacing-sm) var(--menu-spacing-md);
    font-size: 0.95rem;
  }
  
  .voice-icon {
    font-size: 1.1rem;
  }
  
  .last-transcript {
    max-width: 200px;
  }
  
  .voice-help small {
    font-size: 0.8rem;
  }
}

/* تحسينات إضافية للوضوح */
.voice-order-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.3);
}

.voice-order-btn.listening:focus {
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.3);
}

/* تأثير التحميل */
.voice-order-btn.processing::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
