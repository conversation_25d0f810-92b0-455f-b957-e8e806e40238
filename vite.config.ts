import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Load environment variables based on mode
  const env = loadEnv(mode, process.cwd(), '');
  
  // Determine which env file to use
  const envFile = mode === 'local' ? '.env.local' : 
                  mode === 'production' ? '.env.production' : 
                  '.env';
  
  console.log(`🔧 Vite running in ${mode} mode`);
  console.log(`📁 Using env file: ${envFile}`);

  return {
    plugins: [react()],
    server: {
      port: parseInt(env.VITE_FRONTEND_PORT) || 5192,
      host: true,
      open: true,
      strictPort: false, // السماح بتغيير المنفذ إذا كان مستخدماً
      cors: true
    },
  build: {
    outDir: 'dist',
    sourcemap: mode === 'development',
    minify: mode === 'production' ? 'esbuild' : false,
    target: 'es2015',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          socket: ['socket.io-client']
        },
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    },
    chunkSizeWarningLimit: 1000
  },
    optimizeDeps: {
      include: ['react', 'react-dom', 'react-router-dom', 'socket.io-client']
    }
  };
});
