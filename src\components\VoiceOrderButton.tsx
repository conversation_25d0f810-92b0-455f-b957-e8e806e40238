import React, { useState, useCallback } from 'react';
import { useSpeechRecognition } from '../hooks/useSpeechRecognition';
import '../styles/components/VoiceOrderButton.css';

interface MenuItem {
  _id: string;
  name: string;
  price: number;
  description?: string;
  available: boolean;
}

interface VoiceOrderButtonProps {
  menuItems: MenuItem[];
  onAddToCart: (item: MenuItem, quantity?: number) => void;
  onShowMessage: (message: string, type: 'success' | 'error' | 'info') => void;
}

interface ParsedOrder {
  item: MenuItem;
  quantity: number;
}

const VoiceOrderButton: React.FC<VoiceOrderButtonProps> = ({
  menuItems,
  onAddToCart,
  onShowMessage
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastTranscript, setLastTranscript] = useState('');

  // دالة لتحليل النص المنطوق واستخراج الطلبات
  const parseVoiceOrder = useCallback((transcript: string): ParsedOrder[] => {
    const orders: ParsedOrder[] = [];
    const text = transcript.toLowerCase().trim();
    
    console.log('🎤 تحليل النص المنطوق:', text);

    // أنماط مختلفة للأرقام العربية والإنجليزية
    const numberPatterns = {
      'واحد': 1, '1': 1, 'وحدة': 1, 'قطعة': 1,
      'اثنين': 2, 'اثنان': 2, '2': 2, 'قطعتين': 2,
      'ثلاثة': 3, 'ثلاث': 3, '3': 3,
      'أربعة': 4, 'أربع': 4, '4': 4,
      'خمسة': 5, 'خمس': 5, '5': 5,
      'ستة': 6, 'ست': 6, '6': 6,
      'سبعة': 7, 'سبع': 7, '7': 7,
      'ثمانية': 8, 'ثماني': 8, '8': 8,
      'تسعة': 9, 'تسع': 9, '9': 9,
      'عشرة': 10, 'عشر': 10, '10': 10
    };

    // البحث عن الأنماط في النص
    const patterns = [
      // نمط: "رقم + اسم المشروب" مثل "2 شاي" أو "ثلاثة قهوة"
      /(\d+|واحد|اثنين|اثنان|ثلاثة|ثلاث|أربعة|أربع|خمسة|خمس|ستة|ست|سبعة|سبع|ثمانية|ثماني|تسعة|تسع|عشرة|عشر|وحدة|قطعة|قطعتين)\s*([أ-ي\s]+)/g,
      // نمط: "اسم المشروب + رقم" مثل "شاي 2" أو "قهوة ثلاثة"
      /([أ-ي\s]+)\s*(\d+|واحد|اثنين|اثنان|ثلاثة|ثلاث|أربعة|أربع|خمسة|خمس|ستة|ست|سبعة|سبع|ثمانية|ثماني|تسعة|تسع|عشرة|عشر)/g
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        let quantity: number;
        let itemName: string;

        // تحديد الكمية واسم المنتج حسب النمط
        if (/^\d/.test(match[1]) || numberPatterns[match[1]]) {
          // النمط الأول: رقم + اسم
          quantity = numberPatterns[match[1]] || parseInt(match[1]) || 1;
          itemName = match[2].trim();
        } else {
          // النمط الثاني: اسم + رقم
          itemName = match[1].trim();
          quantity = numberPatterns[match[2]] || parseInt(match[2]) || 1;
        }

        // البحث عن المنتج في القائمة
        const foundItem = menuItems.find(item => {
          const itemNameLower = item.name.toLowerCase();
          const searchTerms = itemName.split(/\s+/);
          
          // البحث بالاسم الكامل أو جزء منه
          return searchTerms.some(term => 
            itemNameLower.includes(term) || 
            term.includes(itemNameLower) ||
            // بحث مرن للكلمات المشابهة
            (term.length > 2 && itemNameLower.includes(term.substring(0, 3)))
          );
        });

        if (foundItem && foundItem.available) {
          // التحقق من عدم وجود المنتج مسبقاً في الطلبات
          const existingOrderIndex = orders.findIndex(order => order.item._id === foundItem._id);
          if (existingOrderIndex >= 0) {
            orders[existingOrderIndex].quantity += quantity;
          } else {
            orders.push({ item: foundItem, quantity });
          }
          
          console.log(`✅ تم العثور على: ${foundItem.name} - الكمية: ${quantity}`);
        } else {
          console.log(`❌ لم يتم العثور على: ${itemName}`);
        }
      }
    });

    // إذا لم يتم العثور على أي نمط، جرب البحث البسيط
    if (orders.length === 0) {
      const foundItem = menuItems.find(item => {
        const itemNameLower = item.name.toLowerCase();
        return text.includes(itemNameLower) || itemNameLower.includes(text);
      });

      if (foundItem && foundItem.available) {
        orders.push({ item: foundItem, quantity: 1 });
        console.log(`✅ بحث بسيط - تم العثور على: ${foundItem.name}`);
      }
    }

    return orders;
  }, [menuItems]);

  // دالة معالجة نتيجة التعرف على الصوت
  const handleSpeechResult = useCallback((result: { transcript: string; confidence: number }) => {
    setIsProcessing(true);
    setLastTranscript(result.transcript);
    
    console.log('🎤 النص المنطوق:', result.transcript);
    console.log('🎯 مستوى الثقة:', result.confidence);

    try {
      const orders = parseVoiceOrder(result.transcript);
      
      if (orders.length > 0) {
        let successMessage = 'تم إضافة الطلبات التالية:\n';
        
        orders.forEach(order => {
          // إضافة المنتج إلى السلة
          for (let i = 0; i < order.quantity; i++) {
            onAddToCart(order.item);
          }
          successMessage += `• ${order.quantity} ${order.item.name}\n`;
        });
        
        onShowMessage(successMessage, 'success');
      } else {
        onShowMessage(
          `لم أتمكن من فهم الطلب: "${result.transcript}"\n` +
          'جرب قول شيء مثل: "اثنين شاي وواحد قهوة"',
          'error'
        );
      }
    } catch (error) {
      console.error('خطأ في تحليل الطلب الصوتي:', error);
      onShowMessage('حدث خطأ في تحليل الطلب الصوتي', 'error');
    } finally {
      setIsProcessing(false);
    }
  }, [parseVoiceOrder, onAddToCart, onShowMessage]);

  // دالة معالجة أخطاء التعرف على الصوت
  const handleSpeechError = useCallback((error: string) => {
    setIsProcessing(false);
    onShowMessage(`خطأ في التعرف على الصوت: ${error}`, 'error');
  }, [onShowMessage]);

  // استخدام hook التعرف على الصوت
  const {
    isListening,
    isSupported,
    startListening,
    stopListening
  } = useSpeechRecognition({
    onResult: handleSpeechResult,
    onError: handleSpeechError,
    language: 'ar-SA',
    continuous: false
  });

  // دالة التحكم في التسجيل
  const toggleListening = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  // إذا كان المتصفح لا يدعم التعرف على الصوت
  if (!isSupported) {
    return null;
  }

  return (
    <div className="voice-order-container">
      <button
        className={`voice-order-btn ${isListening ? 'listening' : ''} ${isProcessing ? 'processing' : ''}`}
        onClick={toggleListening}
        disabled={isProcessing}
        title={isListening ? 'اضغط لإيقاف التسجيل' : 'اضغط للطلب بالصوت'}
      >
        <div className="voice-btn-content">
          <i className={`fas ${isListening ? 'fa-stop' : 'fa-microphone'} voice-icon`}></i>
          <span className="voice-btn-text">
            {isProcessing ? 'جاري المعالجة...' : 
             isListening ? 'اضغط للإيقاف' : 'طلب صوتي'}
          </span>
        </div>
        
        {isListening && (
          <div className="voice-animation">
            <div className="wave wave1"></div>
            <div className="wave wave2"></div>
            <div className="wave wave3"></div>
          </div>
        )}
      </button>
      
      {lastTranscript && (
        <div className="last-transcript">
          <small>آخر نص: {lastTranscript}</small>
        </div>
      )}
      
      <div className="voice-help">
        <small>
          <i className="fas fa-info-circle"></i>
          مثال: "اثنين شاي وواحد قهوة"
        </small>
      </div>
    </div>
  );
};

export default VoiceOrderButton;
