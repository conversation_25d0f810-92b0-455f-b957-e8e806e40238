/* =================================== */
/* Chef Dashboard - لوحة الطباخ */
/* =================================== */

/* متغيرات الألوان الخاصة بالطباخ */
:root {
  --chef-primary-color: #2c3e50;
  --chef-secondary-color: #3498db;
  --chef-accent-color: #e74c3c;
  --chef-success-color: #27ae60;
  --chef-warning-color: #f39c12;
  --chef-info-color: #17a2b8;
  --chef-light-color: #ecf0f1;
  --chef-dark-color: #2c3e50;
  
  --chef-bg-primary: #ffffff;
  --chef-bg-secondary: #f8f9fa;
  --chef-bg-tertiary: #e9ecef;
  
  --chef-text-primary: #2c3e50;
  --chef-text-secondary: #6c757d;
  --chef-text-light: #ffffff;
  
  --chef-border-color: #dee2e6;
  --chef-border-radius: 12px;
  --chef-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  --chef-shadow-hover: 0 6px 20px rgba(0, 0, 0, 0.15);
  
  --chef-spacing-xs: 0.25rem;
  --chef-spacing-sm: 0.5rem;
  --chef-spacing-md: 1rem;
  --chef-spacing-lg: 1.5rem;
  --chef-spacing-xl: 2rem;
}

/* Container الرئيسي */
.chef-dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, var(--chef-bg-secondary) 0%, var(--chef-bg-tertiary) 100%);
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header الطباخ */
.chef-header {
  background: linear-gradient(135deg, var(--chef-primary-color), var(--chef-secondary-color));
  color: var(--chef-text-light);
  padding: var(--chef-spacing-lg) var(--chef-spacing-xl);
  box-shadow: var(--chef-shadow);
  z-index: 1000;
  position: relative;
}

.chef-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  pointer-events: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--chef-spacing-lg);
}

.chef-title {
  font-size: 1.8rem;
  font-weight: 800;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.chef-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--chef-spacing-md);
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: var(--chef-text-light);
  padding: var(--chef-spacing-sm) var(--chef-spacing-md);
  border-radius: var(--chef-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--chef-spacing-sm);
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.logout-btn {
  background: var(--chef-accent-color);
  border: none;
  color: var(--chef-text-light);
  padding: var(--chef-spacing-sm) var(--chef-spacing-md);
  border-radius: var(--chef-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--chef-spacing-sm);
}

.logout-btn:hover {
  background: #c0392b;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

/* محتوى لوحة التحكم */
.dashboard-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* الشريط الجانبي */
.chef-sidebar {
  width: 320px;
  background: var(--chef-bg-primary);
  border-left: 1px solid var(--chef-border-color);
  box-shadow: var(--chef-shadow);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  z-index: 100;
}

.chef-sidebar.open {
  transform: translateX(0);
}

.sidebar-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--chef-spacing-lg);
}

/* ملف الطباخ */
.chef-profile {
  text-align: center;
  padding: var(--chef-spacing-lg);
  border-bottom: 2px solid var(--chef-border-color);
  margin-bottom: var(--chef-spacing-lg);
}

.chef-avatar {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--chef-primary-color), var(--chef-secondary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  margin: 0 auto var(--chef-spacing-md);
  box-shadow: var(--chef-shadow);
}

.chef-profile h3 {
  color: var(--chef-text-primary);
  margin: 0 0 var(--chef-spacing-xs) 0;
  font-size: 1.3rem;
  font-weight: 700;
}

.chef-profile p {
  color: var(--chef-text-secondary);
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

/* التنقل والفلاتر */
.filter-nav {
  display: flex;
  flex-direction: column;
  gap: var(--chef-spacing-sm);
}

.filter-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--chef-spacing-md);
  background: var(--chef-bg-secondary);
  border: 2px solid transparent;
  border-radius: var(--chef-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: right;
  font-weight: 600;
  color: var(--chef-text-primary);
}

.filter-btn:hover {
  background: var(--chef-bg-tertiary);
  border-color: var(--chef-secondary-color);
  transform: translateX(-3px);
}

.filter-btn.active {
  background: linear-gradient(135deg, var(--chef-secondary-color), var(--chef-primary-color));
  color: var(--chef-text-light);
  border-color: var(--chef-primary-color);
  box-shadow: var(--chef-shadow);
}

.filter-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.filter-btn-content {
  display: flex;
  align-items: center;
  gap: var(--chef-spacing-sm);
}

.filter-icon {
  font-size: 1.2rem;
  min-width: 24px;
  text-align: center;
}

.filter-text {
  flex: 1;
  font-size: 1rem;
}

.filter-count {
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
  padding: var(--chef-spacing-xs) var(--chef-spacing-sm);
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 700;
  min-width: 24px;
  text-align: center;
}

.filter-btn.active .filter-count {
  background: rgba(255, 255, 255, 0.3);
}

/* المحتوى الرئيسي */
.chef-main {
  flex: 1;
  padding: var(--chef-spacing-xl);
  overflow-y: auto;
  background: var(--chef-bg-secondary);
}

/* قسم الطلبات */
.orders-section {
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--chef-spacing-xl);
  padding: var(--chef-spacing-lg);
  background: var(--chef-bg-primary);
  border-radius: var(--chef-border-radius);
  box-shadow: var(--chef-shadow);
}

.section-header h2 {
  color: var(--chef-text-primary);
  margin: 0;
  font-size: 1.6rem;
  font-weight: 700;
}

.orders-count {
  background: var(--chef-secondary-color);
  color: var(--chef-text-light);
  padding: var(--chef-spacing-sm) var(--chef-spacing-md);
  border-radius: 20px;
  font-weight: 700;
  font-size: 0.95rem;
}

/* شبكة الطلبات */
.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: var(--chef-spacing-lg);
  margin-bottom: var(--chef-spacing-xl);
  padding: 0 var(--chef-spacing-sm);
}

/* بطاقة الطلب المحسنة */
.order-card {
  background: var(--chef-bg-primary);
  border: 2px solid var(--chef-border-color);
  border-radius: var(--chef-border-radius);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: var(--chef-shadow);
  position: relative;
  min-height: 280px;
  max-height: 450px;
  display: flex;
  flex-direction: column;
}

.order-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--chef-shadow-lg);
  border-color: var(--chef-secondary-color);
}

.order-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg,
    var(--chef-secondary-color) 0%,
    var(--chef-primary-color) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.order-card:hover::before {
  opacity: 1;
}

/* حالات الطلبات */
.order-card.pending {
  border-left: 6px solid var(--chef-warning-color);
}

.order-card.preparing {
  border-left: 6px solid var(--chef-info-color);
}

.order-card.ready {
  border-left: 6px solid var(--chef-success-color);
}

.order-card.completed {
  border-left: 6px solid var(--chef-success-color);
  opacity: 0.8;
}

/* رأس الطلب المحسن */
.order-header {
  padding: var(--chef-spacing-md) var(--chef-spacing-lg);
  background: linear-gradient(135deg, var(--chef-bg-secondary) 0%, var(--chef-bg-tertiary) 100%);
  border-bottom: 1px solid var(--chef-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  min-height: 60px;
}

.order-number {
  font-size: 1.4rem;
  font-weight: 800;
  color: var(--chef-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--chef-spacing-sm);
}

.order-number::before {
  content: '📋';
  font-size: 1.2rem;
}

.order-status {
  padding: var(--chef-spacing-sm) var(--chef-spacing-md);
  border-radius: 25px;
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  min-width: 80px;
  text-align: center;
}

.order-status.pending {
  background: var(--chef-warning-color);
  color: white;
}

.order-status.preparing {
  background: var(--chef-info-color);
  color: white;
}

.order-status.ready {
  background: var(--chef-success-color);
  color: white;
}

.order-status.completed {
  background: var(--chef-success-color);
  color: white;
}

/* معلومات الطلب المحسنة */
.order-info {
  padding: var(--chef-spacing-md) var(--chef-spacing-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--chef-spacing-sm);
}

.order-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--chef-spacing-sm);
  margin-bottom: var(--chef-spacing-sm);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--chef-spacing-xs);
  padding: var(--chef-spacing-sm);
  background: var(--chef-bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--chef-border-color);
  transition: all 0.3s ease;
}

.detail-item:hover {
  background: var(--chef-bg-tertiary);
  border-color: var(--chef-secondary-color);
}

.detail-label {
  font-size: 0.75rem;
  color: var(--chef-text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: var(--chef-spacing-xs);
}

.detail-label::before {
  content: '•';
  color: var(--chef-secondary-color);
  font-weight: 900;
}

.detail-value {
  font-size: 1rem;
  color: var(--chef-text-primary);
  font-weight: 700;
}

/* عناصر الطلب المحسنة */
.order-items {
  margin-top: var(--chef-spacing-sm);
  flex: 1;
  overflow-y: auto;
  max-height: 200px;
}

.items-header {
  font-size: 0.95rem;
  font-weight: 700;
  color: var(--chef-text-primary);
  margin-bottom: var(--chef-spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--chef-spacing-sm);
  padding: var(--chef-spacing-xs) 0;
  border-bottom: 2px solid var(--chef-border-color);
}

.items-header::before {
  content: '🍽️';
  font-size: 1rem;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: var(--chef-spacing-xs);
  padding-right: var(--chef-spacing-xs);
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--chef-spacing-sm);
  background: linear-gradient(135deg, var(--chef-bg-secondary) 0%, var(--chef-bg-tertiary) 100%);
  border-radius: 10px;
  border: 1px solid var(--chef-border-color);
  transition: all 0.3s ease;
  position: relative;
}

.order-item:hover {
  background: linear-gradient(135deg, var(--chef-bg-tertiary) 0%, var(--chef-bg-quaternary) 100%);
  border-color: var(--chef-secondary-color);
  transform: translateX(-2px);
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--chef-spacing-xs);
}

.item-name {
  font-weight: 700;
  color: var(--chef-text-primary);
  font-size: 0.95rem;
  line-height: 1.3;
}

.item-notes {
  font-size: 0.8rem;
  color: var(--chef-text-secondary);
  font-style: italic;
  background: rgba(255, 255, 255, 0.7);
  padding: var(--chef-spacing-xs);
  border-radius: 6px;
  border-left: 3px solid var(--chef-info-color);
}

.item-quantity {
  background: linear-gradient(135deg, var(--chef-secondary-color), var(--chef-primary-color));
  color: white;
  padding: var(--chef-spacing-xs) var(--chef-spacing-sm);
  border-radius: 25px;
  font-weight: 800;
  font-size: 0.85rem;
  min-width: 35px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
  border: 2px solid white;
}

/* وقت الطلب */
.order-time {
  padding: var(--chef-spacing-md) var(--chef-spacing-lg);
  background: var(--chef-bg-tertiary);
  border-top: 1px solid var(--chef-border-color);
  font-size: 0.9rem;
  color: var(--chef-text-secondary);
  text-align: center;
}

/* أزرار الإجراءات المحسنة */
.order-actions {
  padding: var(--chef-spacing-md);
  background: linear-gradient(135deg, var(--chef-bg-secondary) 0%, var(--chef-bg-tertiary) 100%);
  border-top: 2px solid var(--chef-border-color);
  display: flex;
  gap: var(--chef-spacing-sm);
  margin-top: auto;
}

.action-btn {
  flex: 1;
  padding: var(--chef-spacing-sm) var(--chef-spacing-md);
  border: 2px solid transparent;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  font-weight: 700;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--chef-spacing-xs);
  position: relative;
  overflow: hidden;
  min-height: 40px;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.action-btn:active {
  transform: translateY(-1px);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.action-btn:disabled::before {
  display: none;
}

.btn-accept {
  background: linear-gradient(135deg, var(--chef-success-color), #20c997);
  color: white;
  border-color: var(--chef-success-color);
}

.btn-accept:hover {
  background: linear-gradient(135deg, #20c997, #17a2b8);
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

.btn-ready {
  background: linear-gradient(135deg, var(--chef-info-color), #138496);
  color: white;
  border-color: var(--chef-info-color);
}

.btn-ready:hover {
  background: linear-gradient(135deg, #138496, #117a8b);
  box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
}

.btn-details {
  background: linear-gradient(135deg, var(--chef-secondary-color), #2980b9);
  color: white;
  border-color: var(--chef-secondary-color);
}

.btn-details:hover {
  background: linear-gradient(135deg, #2980b9, #21618c);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

/* حالة التحميل */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--chef-spacing-xl);
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid var(--chef-border-color);
  border-top: 4px solid var(--chef-secondary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--chef-spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  color: var(--chef-text-secondary);
  font-size: 1.1rem;
  margin: 0;
}

/* حالة فارغة */
.empty-state {
  text-align: center;
  padding: var(--chef-spacing-xl);
  color: var(--chef-text-secondary);
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: var(--chef-spacing-lg);
  color: var(--chef-border-color);
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: var(--chef-spacing-sm);
  color: var(--chef-text-primary);
}

.empty-state p {
  font-size: 1.1rem;
  margin: 0;
}

/* زر تبديل الشريط الجانبي للموبايل */
.sidebar-toggle {
  display: none;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: var(--chef-text-light);
  padding: var(--chef-spacing-sm);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

/* =================================== */
/* شاشة تفاصيل الطلب (Modal) */
/* =================================== */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--chef-z-modal);
  padding: var(--chef-spacing-lg);
  animation: chef-fade-in 0.3s ease-out;
}

.modal-content {
  background: var(--chef-bg-primary);
  border-radius: var(--chef-border-radius-lg);
  box-shadow: var(--chef-shadow-xl);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: chef-slide-in-up 0.3s ease-out;
}

.modal-header {
  padding: var(--chef-spacing-lg);
  background: linear-gradient(135deg, var(--chef-primary-color), var(--chef-secondary-color));
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: var(--chef-border-radius-lg) var(--chef-border-radius-lg) 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 800;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: rotate(90deg);
}

.modal-body {
  padding: var(--chef-spacing-lg);
}

.modal-order-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--chef-spacing-md);
  margin-bottom: var(--chef-spacing-lg);
}

.modal-detail-card {
  background: var(--chef-bg-secondary);
  padding: var(--chef-spacing-md);
  border-radius: var(--chef-border-radius);
  border: 2px solid var(--chef-border-color);
  transition: all 0.3s ease;
}

.modal-detail-card:hover {
  background: var(--chef-bg-tertiary);
  border-color: var(--chef-secondary-color);
}

.modal-detail-label {
  font-size: 0.85rem;
  color: var(--chef-text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--chef-spacing-xs);
}

.modal-detail-value {
  font-size: 1.1rem;
  color: var(--chef-text-primary);
  font-weight: 700;
}

.modal-items-section {
  margin-bottom: var(--chef-spacing-lg);
}

.modal-items-header {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--chef-text-primary);
  margin-bottom: var(--chef-spacing-md);
  padding-bottom: var(--chef-spacing-sm);
  border-bottom: 2px solid var(--chef-border-color);
  display: flex;
  align-items: center;
  gap: var(--chef-spacing-sm);
}

.modal-items-header::before {
  content: '🍽️';
  font-size: 1.5rem;
}

.modal-items-list {
  display: flex;
  flex-direction: column;
  gap: var(--chef-spacing-sm);
}

.modal-order-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--chef-spacing-md);
  background: linear-gradient(135deg, var(--chef-bg-secondary) 0%, var(--chef-bg-tertiary) 100%);
  border-radius: var(--chef-border-radius);
  border: 2px solid var(--chef-border-color);
  transition: all 0.3s ease;
}

.modal-order-item:hover {
  border-color: var(--chef-secondary-color);
  transform: translateX(-3px);
}

.modal-item-info {
  flex: 1;
}

.modal-item-name {
  font-weight: 700;
  color: var(--chef-text-primary);
  font-size: 1.1rem;
  margin-bottom: var(--chef-spacing-xs);
}

.modal-item-notes {
  font-size: 0.9rem;
  color: var(--chef-text-secondary);
  font-style: italic;
  background: rgba(255, 255, 255, 0.8);
  padding: var(--chef-spacing-sm);
  border-radius: 6px;
  border-left: 4px solid var(--chef-info-color);
}

.modal-item-quantity {
  background: linear-gradient(135deg, var(--chef-secondary-color), var(--chef-primary-color));
  color: white;
  padding: var(--chef-spacing-sm) var(--chef-spacing-md);
  border-radius: 30px;
  font-weight: 800;
  font-size: 1rem;
  min-width: 40px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
  border: 3px solid white;
}

.modal-actions {
  padding: var(--chef-spacing-lg);
  background: var(--chef-bg-secondary);
  border-top: 2px solid var(--chef-border-color);
  display: flex;
  gap: var(--chef-spacing-md);
  border-radius: 0 0 var(--chef-border-radius-lg) var(--chef-border-radius-lg);
}

.modal-action-btn {
  flex: 1;
  padding: var(--chef-spacing-md) var(--chef-spacing-lg);
  border: 2px solid transparent;
  border-radius: var(--chef-border-radius);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  font-weight: 700;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--chef-spacing-sm);
  position: relative;
  overflow: hidden;
  min-height: 50px;
}

.modal-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.modal-action-btn:hover::before {
  left: 100%;
}

.modal-action-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.accept-btn {
  background: linear-gradient(135deg, var(--chef-success-color), #20c997);
  color: white;
  border-color: var(--chef-success-color);
}

.accept-btn:hover {
  background: linear-gradient(135deg, #20c997, #17a2b8);
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
}

.complete-btn {
  background: linear-gradient(135deg, var(--chef-info-color), #138496);
  color: white;
  border-color: var(--chef-info-color);
}

.complete-btn:hover {
  background: linear-gradient(135deg, #138496, #117a8b);
  box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
}

.close-modal-btn {
  background: linear-gradient(135deg, var(--chef-text-secondary), #5a6268);
  color: white;
  border-color: var(--chef-text-secondary);
}

.close-modal-btn:hover {
  background: linear-gradient(135deg, #5a6268, #495057);
  box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
}

/* أنيميشن انزلاق للأعلى */
@keyframes chef-slide-in-up {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* الاستجابة للشاشات المختلفة */
@media (max-width: 1024px) {
  .chef-sidebar {
    position: fixed;
    top: 0;
    right: -320px;
    height: 100vh;
    z-index: 1001;
    transition: right 0.3s ease;
  }

  .chef-sidebar.open {
    right: 0;
  }

  .sidebar-toggle {
    display: block;
  }

  .orders-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--chef-spacing-md);
  }

  .order-card {
    min-height: 260px;
    max-height: 400px;
  }

  .modal-content {
    max-width: 90%;
    margin: var(--chef-spacing-md);
  }

  .modal-order-details {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .chef-header {
    padding: var(--chef-spacing-md) var(--chef-spacing-lg);
  }

  .chef-main {
    padding: var(--chef-spacing-md);
  }

  .orders-grid {
    grid-template-columns: 1fr;
    gap: var(--chef-spacing-md);
    padding: 0;
  }

  .section-header {
    flex-direction: column;
    gap: var(--chef-spacing-md);
    align-items: stretch;
    text-align: center;
    padding: var(--chef-spacing-md);
  }

  .order-card {
    min-height: 240px;
    max-height: 380px;
  }

  .order-actions {
    flex-direction: column;
    gap: var(--chef-spacing-sm);
  }

  .order-details {
    grid-template-columns: 1fr;
    gap: var(--chef-spacing-sm);
  }

  .detail-item {
    padding: var(--chef-spacing-sm);
  }

  .order-items {
    max-height: 150px;
  }

  .modal-overlay {
    padding: var(--chef-spacing-sm);
  }

  .modal-content {
    max-width: 100%;
    max-height: 95vh;
  }

  .modal-header {
    padding: var(--chef-spacing-md);
  }

  .modal-body {
    padding: var(--chef-spacing-md);
  }

  .modal-actions {
    flex-direction: column;
    gap: var(--chef-spacing-sm);
  }

  .modal-action-btn {
    min-height: 45px;
  }
}

@media (max-width: 576px) {
  .chef-header {
    padding: var(--chef-spacing-sm) var(--chef-spacing-md);
  }

  .chef-title {
    font-size: 1.4rem;
  }

  .chef-subtitle {
    font-size: 0.9rem;
  }

  .chef-main {
    padding: var(--chef-spacing-sm);
  }

  .orders-grid {
    gap: var(--chef-spacing-sm);
  }

  .order-card {
    border-radius: 10px;
    min-height: 220px;
    max-height: 350px;
  }

  .order-header {
    padding: var(--chef-spacing-sm) var(--chef-spacing-md);
    min-height: 50px;
  }

  .order-number {
    font-size: 1.2rem;
  }

  .order-status {
    font-size: 0.7rem;
    padding: var(--chef-spacing-xs) var(--chef-spacing-sm);
    min-width: 70px;
  }

  .order-info {
    padding: var(--chef-spacing-sm) var(--chef-spacing-md);
  }

  .detail-item {
    padding: var(--chef-spacing-xs) var(--chef-spacing-sm);
  }

  .detail-label {
    font-size: 0.7rem;
  }

  .detail-value {
    font-size: 0.9rem;
  }

  .order-items {
    max-height: 120px;
  }

  .items-header {
    font-size: 0.85rem;
  }

  .order-item {
    padding: var(--chef-spacing-xs) var(--chef-spacing-sm);
  }

  .item-name {
    font-size: 0.85rem;
  }

  .item-notes {
    font-size: 0.75rem;
    padding: var(--chef-spacing-xs);
  }

  .item-quantity {
    font-size: 0.8rem;
    min-width: 30px;
    padding: var(--chef-spacing-xs);
  }

  .order-actions {
    padding: var(--chef-spacing-sm);
  }

  .action-btn {
    font-size: 0.8rem;
    min-height: 35px;
    padding: var(--chef-spacing-xs) var(--chef-spacing-sm);
  }

  .chef-sidebar {
    width: 100%;
    right: -100%;
  }

  .sidebar-content {
    padding: var(--chef-spacing-sm);
  }

  .chef-profile {
    padding: var(--chef-spacing-md);
  }

  .chef-avatar {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }

  .filter-btn {
    padding: var(--chef-spacing-sm);
  }

  .filter-text {
    font-size: 0.9rem;
  }

  .filter-count {
    font-size: 0.8rem;
    padding: var(--chef-spacing-xs);
  }

  /* تحسينات Modal للشاشات الصغيرة */
  .modal-overlay {
    padding: var(--chef-spacing-xs);
  }

  .modal-content {
    border-radius: var(--chef-border-radius);
  }

  .modal-header {
    padding: var(--chef-spacing-sm) var(--chef-spacing-md);
  }

  .modal-header h3 {
    font-size: 1.3rem;
  }

  .close-btn {
    width: 35px;
    height: 35px;
    font-size: 1.3rem;
  }

  .modal-body {
    padding: var(--chef-spacing-sm) var(--chef-spacing-md);
  }

  .modal-detail-card {
    padding: var(--chef-spacing-sm);
  }

  .modal-detail-label {
    font-size: 0.8rem;
  }

  .modal-detail-value {
    font-size: 1rem;
  }

  .modal-items-header {
    font-size: 1.1rem;
  }

  .modal-order-item {
    padding: var(--chef-spacing-sm);
  }

  .modal-item-name {
    font-size: 1rem;
  }

  .modal-item-notes {
    font-size: 0.85rem;
    padding: var(--chef-spacing-xs) var(--chef-spacing-sm);
  }

  .modal-item-quantity {
    font-size: 0.9rem;
    min-width: 35px;
    padding: var(--chef-spacing-xs) var(--chef-spacing-sm);
  }

  .modal-actions {
    padding: var(--chef-spacing-sm) var(--chef-spacing-md);
  }

  .modal-action-btn {
    font-size: 0.9rem;
    min-height: 40px;
    padding: var(--chef-spacing-sm);
  }
}
