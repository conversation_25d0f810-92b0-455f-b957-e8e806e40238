/* =================================== */
/* Chef Styles - ملفات تنسيق الطباخ */
/* =================================== */

/* استيراد متغيرات الطباخ أولاً */
@import '../variables/chef-variables.css';

/* استيراد جميع ملفات CSS الخاصة بالطباخ */
@import './ChefDashboard.css';

/* =================================== */
/* متغيرات عامة للطباخ */
/* =================================== */

:root {
  /* ألوان الطباخ الأساسية */
  --chef-brand-primary: #2c3e50;
  --chef-brand-secondary: #3498db;
  --chef-brand-accent: #e74c3c;
  
  /* ألوان الحالات */
  --chef-status-pending: #f39c12;
  --chef-status-preparing: #17a2b8;
  --chef-status-ready: #28a745;
  --chef-status-completed: #6c757d;
  
  /* ألوان الخلفية */
  --chef-bg-main: #f8f9fa;
  --chef-bg-card: #ffffff;
  --chef-bg-sidebar: #ffffff;
  --chef-bg-header: linear-gradient(135deg, #2c3e50, #3498db);
  
  /* ألوان النصوص */
  --chef-text-primary: #2c3e50;
  --chef-text-secondary: #6c757d;
  --chef-text-muted: #adb5bd;
  --chef-text-white: #ffffff;
  
  /* الحدود والظلال */
  --chef-border-light: #dee2e6;
  --chef-border-medium: #ced4da;
  --chef-border-dark: #adb5bd;
  --chef-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --chef-shadow-md: 0 4px 15px rgba(0, 0, 0, 0.1);
  --chef-shadow-lg: 0 6px 20px rgba(0, 0, 0, 0.15);
  
  /* المسافات */
  --chef-space-xs: 0.25rem;
  --chef-space-sm: 0.5rem;
  --chef-space-md: 1rem;
  --chef-space-lg: 1.5rem;
  --chef-space-xl: 2rem;
  --chef-space-xxl: 3rem;
  
  /* أحجام الخطوط */
  --chef-font-xs: 0.75rem;
  --chef-font-sm: 0.875rem;
  --chef-font-md: 1rem;
  --chef-font-lg: 1.125rem;
  --chef-font-xl: 1.25rem;
  --chef-font-xxl: 1.5rem;
  --chef-font-xxxl: 2rem;
  
  /* نصف القطر */
  --chef-radius-sm: 6px;
  --chef-radius-md: 8px;
  --chef-radius-lg: 12px;
  --chef-radius-xl: 16px;
  --chef-radius-round: 50%;
  
  /* الانتقالات */
  --chef-transition-fast: 0.15s ease;
  --chef-transition-normal: 0.3s ease;
  --chef-transition-slow: 0.5s ease;
  
  /* العرض والارتفاع */
  --chef-sidebar-width: 320px;
  --chef-header-height: 80px;
  --chef-card-min-height: 200px;
}

/* =================================== */
/* إعدادات عامة للطباخ */
/* =================================== */

/* تطبيق الخط العربي */
.chef-dashboard,
.chef-dashboard * {
  font-family: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
  direction: rtl;
  text-align: right;
}

/* إعادة تعيين الهوامش والحشو */
.chef-dashboard * {
  box-sizing: border-box;
}

/* تحسين النصوص العربية */
.chef-dashboard {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* =================================== */
/* فئات مساعدة للطباخ */
/* =================================== */

/* المسافات */
.chef-m-0 { margin: 0 !important; }
.chef-m-1 { margin: var(--chef-space-xs) !important; }
.chef-m-2 { margin: var(--chef-space-sm) !important; }
.chef-m-3 { margin: var(--chef-space-md) !important; }
.chef-m-4 { margin: var(--chef-space-lg) !important; }
.chef-m-5 { margin: var(--chef-space-xl) !important; }

.chef-p-0 { padding: 0 !important; }
.chef-p-1 { padding: var(--chef-space-xs) !important; }
.chef-p-2 { padding: var(--chef-space-sm) !important; }
.chef-p-3 { padding: var(--chef-space-md) !important; }
.chef-p-4 { padding: var(--chef-space-lg) !important; }
.chef-p-5 { padding: var(--chef-space-xl) !important; }

/* الألوان */
.chef-text-primary { color: var(--chef-text-primary) !important; }
.chef-text-secondary { color: var(--chef-text-secondary) !important; }
.chef-text-muted { color: var(--chef-text-muted) !important; }
.chef-text-white { color: var(--chef-text-white) !important; }

.chef-bg-primary { background-color: var(--chef-brand-primary) !important; }
.chef-bg-secondary { background-color: var(--chef-brand-secondary) !important; }
.chef-bg-light { background-color: var(--chef-bg-main) !important; }
.chef-bg-white { background-color: var(--chef-bg-card) !important; }

/* الحدود */
.chef-border { border: 1px solid var(--chef-border-light) !important; }
.chef-border-top { border-top: 1px solid var(--chef-border-light) !important; }
.chef-border-bottom { border-bottom: 1px solid var(--chef-border-light) !important; }
.chef-border-left { border-left: 1px solid var(--chef-border-light) !important; }
.chef-border-right { border-right: 1px solid var(--chef-border-light) !important; }

.chef-rounded { border-radius: var(--chef-radius-md) !important; }
.chef-rounded-lg { border-radius: var(--chef-radius-lg) !important; }
.chef-rounded-xl { border-radius: var(--chef-radius-xl) !important; }
.chef-rounded-circle { border-radius: var(--chef-radius-round) !important; }

/* الظلال */
.chef-shadow-sm { box-shadow: var(--chef-shadow-sm) !important; }
.chef-shadow-md { box-shadow: var(--chef-shadow-md) !important; }
.chef-shadow-lg { box-shadow: var(--chef-shadow-lg) !important; }

/* العرض والارتفاع */
.chef-w-100 { width: 100% !important; }
.chef-h-100 { height: 100% !important; }

/* Flexbox */
.chef-d-flex { display: flex !important; }
.chef-flex-column { flex-direction: column !important; }
.chef-flex-row { flex-direction: row !important; }
.chef-justify-content-center { justify-content: center !important; }
.chef-justify-content-between { justify-content: space-between !important; }
.chef-align-items-center { align-items: center !important; }
.chef-align-items-start { align-items: flex-start !important; }
.chef-align-items-end { align-items: flex-end !important; }

/* النصوص */
.chef-text-center { text-align: center !important; }
.chef-text-left { text-align: left !important; }
.chef-text-right { text-align: right !important; }

.chef-font-weight-normal { font-weight: 400 !important; }
.chef-font-weight-bold { font-weight: 700 !important; }
.chef-font-weight-bolder { font-weight: 900 !important; }

.chef-font-size-sm { font-size: var(--chef-font-sm) !important; }
.chef-font-size-md { font-size: var(--chef-font-md) !important; }
.chef-font-size-lg { font-size: var(--chef-font-lg) !important; }
.chef-font-size-xl { font-size: var(--chef-font-xl) !important; }

/* =================================== */
/* أنيميشنز خاصة بالطباخ */
/* =================================== */

/* تأثير النبض للطلبات الجديدة */
@keyframes chef-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
  }
  70% {
    transform: scale(1.02);
    box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}

.chef-pulse {
  animation: chef-pulse 2s infinite;
}

/* تأثير الاهتزاز للتنبيهات */
@keyframes chef-shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

.chef-shake {
  animation: chef-shake 0.5s ease-in-out;
}

/* تأثير التلاشي */
@keyframes chef-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chef-fade-in {
  animation: chef-fade-in 0.3s ease-out;
}

/* تأثير الانزلاق */
@keyframes chef-slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.chef-slide-in-right {
  animation: chef-slide-in-right 0.3s ease-out;
}

/* =================================== */
/* تحسينات الأداء */
/* =================================== */

/* تحسين الرسوم المتحركة */
.chef-dashboard * {
  will-change: auto;
}

.chef-dashboard .order-card,
.chef-dashboard .filter-btn,
.chef-dashboard .action-btn {
  will-change: transform, box-shadow;
}

/* تحسين التمرير */
.chef-main {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* =================================== */
/* طباعة */
/* =================================== */

@media print {
  .chef-sidebar,
  .chef-header .header-right,
  .order-actions {
    display: none !important;
  }
  
  .chef-main {
    padding: 0 !important;
  }
  
  .order-card {
    break-inside: avoid;
    margin-bottom: 1rem;
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
  
  .orders-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }
}

/* =================================== */
/* إمكانية الوصول */
/* =================================== */

/* تحسين التركيز */
.chef-dashboard button:focus,
.chef-dashboard input:focus,
.chef-dashboard select:focus {
  outline: 2px solid var(--chef-brand-secondary);
  outline-offset: 2px;
}

/* تحسين التباين */
@media (prefers-contrast: high) {
  :root {
    --chef-border-light: #000000;
    --chef-text-secondary: #000000;
    --chef-bg-main: #ffffff;
  }
}

/* تقليل الحركة للمستخدمين الذين يفضلون ذلك */
@media (prefers-reduced-motion: reduce) {
  .chef-dashboard * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* =================================== */
/* الوضع المظلم (اختياري) */
/* =================================== */

@media (prefers-color-scheme: dark) {
  .chef-dashboard.dark-mode {
    --chef-bg-main: #1a1a1a;
    --chef-bg-card: #2d2d2d;
    --chef-bg-sidebar: #2d2d2d;
    --chef-text-primary: #ffffff;
    --chef-text-secondary: #cccccc;
    --chef-text-muted: #999999;
    --chef-border-light: #404040;
    --chef-border-medium: #555555;
    --chef-border-dark: #666666;
  }
}
