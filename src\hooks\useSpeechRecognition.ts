import { useState, useEffect, useRef } from 'react';

interface SpeechRecognitionResult {
  transcript: string;
  confidence: number;
}

interface UseSpeechRecognitionProps {
  onResult?: (result: SpeechRecognitionResult) => void;
  onError?: (error: string) => void;
  language?: string;
  continuous?: boolean;
}

interface SpeechRecognitionHook {
  isListening: boolean;
  transcript: string;
  confidence: number;
  isSupported: boolean;
  startListening: () => void;
  stopListening: () => void;
  resetTranscript: () => void;
}

// تعريف واجهة SpeechRecognition للمتصفح
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

export const useSpeechRecognition = ({
  onResult,
  onError,
  language = 'ar-SA', // اللغة العربية السعودية
  continuous = false
}: UseSpeechRecognitionProps = {}): SpeechRecognitionHook => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [confidence, setConfidence] = useState(0);
  const [isSupported, setIsSupported] = useState(false);
  
  const recognitionRef = useRef<any>(null);

  useEffect(() => {
    // التحقق من دعم المتصفح للتعرف على الصوت
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    
    if (SpeechRecognition) {
      setIsSupported(true);
      recognitionRef.current = new SpeechRecognition();
      
      const recognition = recognitionRef.current;
      
      // إعدادات التعرف على الصوت
      recognition.continuous = continuous;
      recognition.interimResults = true;
      recognition.lang = language;
      recognition.maxAlternatives = 1;

      // عند بدء التسجيل
      recognition.onstart = () => {
        setIsListening(true);
      };

      // عند انتهاء التسجيل
      recognition.onend = () => {
        setIsListening(false);
      };

      // عند الحصول على نتيجة
      recognition.onresult = (event: any) => {
        let finalTranscript = '';
        let finalConfidence = 0;

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];
          if (result.isFinal) {
            finalTranscript += result[0].transcript;
            finalConfidence = result[0].confidence;
          }
        }

        if (finalTranscript) {
          setTranscript(finalTranscript);
          setConfidence(finalConfidence);
          
          if (onResult) {
            onResult({
              transcript: finalTranscript,
              confidence: finalConfidence
            });
          }
        }
      };

      // عند حدوث خطأ
      recognition.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error);
        setIsListening(false);
        
        let errorMessage = 'حدث خطأ في التعرف على الصوت';
        
        switch (event.error) {
          case 'no-speech':
            errorMessage = 'لم يتم اكتشاف أي صوت';
            break;
          case 'audio-capture':
            errorMessage = 'لا يمكن الوصول إلى الميكروفون';
            break;
          case 'not-allowed':
            errorMessage = 'تم رفض الإذن للوصول إلى الميكروفون';
            break;
          case 'network':
            errorMessage = 'خطأ في الشبكة';
            break;
          case 'language-not-supported':
            errorMessage = 'اللغة غير مدعومة';
            break;
        }
        
        if (onError) {
          onError(errorMessage);
        }
      };
    } else {
      setIsSupported(false);
      console.warn('Speech Recognition API is not supported in this browser');
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.abort();
      }
    };
  }, [language, continuous, onResult, onError]);

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      setTranscript('');
      setConfidence(0);
      recognitionRef.current.start();
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
    }
  };

  const resetTranscript = () => {
    setTranscript('');
    setConfidence(0);
  };

  return {
    isListening,
    transcript,
    confidence,
    isSupported,
    startListening,
    stopListening,
    resetTranscript
  };
};
