import React, { useState, useEffect, useCallback } from 'react';
import { authenticatedGet, authenticatedPut } from './utils/apiHelpers';
import { useToast } from './hooks/useToast';
import { notificationSound } from './utils/notificationSound';
import { backgroundNotificationService } from './utils/backgroundNotifications';
import socket from './socket';
// استيراد ملفات CSS الخاصة بالطباخ
import './styles/chef/index.css';

interface ChefDashboardProps {
  user?: any;
  onLogout?: () => void;
}

interface OrderItem {
  _id: string;
  name: string;
  price: number;
  quantity: number;
  notes?: string;
  specialRequests?: string;
  modifications?: string[];
  category?: string;
}

interface Order {
  _id: string;
  orderNumber: string;
  items: OrderItem[];
  totalAmount: number;
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'delivered';
  tableNumber?: number;
  customerName?: string;
  waiterName?: string;
  chefName?: string;
  createdAt: string;
  updatedAt: string;
  preparationTime?: number;
  notes?: string;
}

type FilterType = 'pending' | 'preparing' | 'ready' | 'all';

export default function ChefDashboard({ user: propUser, onLogout }: ChefDashboardProps) {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentFilter, setCurrentFilter] = useState<FilterType>('pending');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  
  // Smart loading states for each filter
  const [loadingStates, setLoadingStates] = useState({
    pending: false,
    preparing: false,
    ready: false,
    all: false
  });
  
  // Cache for orders data
  const [ordersCache, setOrdersCache] = useState<{[key: string]: Order[]}>({});
  const [lastFetchTime, setLastFetchTime] = useState<{[key: string]: number}>({});
  
  // Real-time states
  const [isSocketConnected, setIsSocketConnected] = useState(false);
  const [pendingOrdersCount, setPendingOrdersCount] = useState(0);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [ordersPerPage, setOrdersPerPage] = useState(10);
  const [showAllOrders, setShowAllOrders] = useState(false);

  const { showSuccess, showError, showInfo } = useToast();

  // الحصول على بيانات المستخدم
  const user = propUser || JSON.parse(localStorage.getItem('user') || 'null');
  const chefName = user?.username || user?.name || localStorage.getItem('username') || 'الطباخ';
  const chefId = user?._id || user?.id || 'chef-user';
  
  // Smart data loading function based on filter
  const loadFilterData = useCallback(async (filter: FilterType) => {
    const CACHE_DURATION = 2 * 60 * 1000; // 2 minutes
    const now = Date.now();
    
    // Check if we have recent cached data
    if (ordersCache[filter] && lastFetchTime[filter] && (now - lastFetchTime[filter]) < CACHE_DURATION) {
      console.log(`📋 استخدام البيانات المحفوظة للفلتر: ${filter}`);
      setOrders(ordersCache[filter]);
      return;
    }

    try {
      setLoadingStates(prev => ({ ...prev, [filter]: true }));
      console.log(`🔄 تحميل بيانات للفلتر: ${filter}`);

      const response = await authenticatedGet('/api/v1/orders');
      
      console.log('📊 استجابة API للطلبات:', response);
      
      let allOrders: Order[] = [];
      
      // تحقق من أن الاستجابة مصفوفة مباشرة أو تحتوي على data
      if (Array.isArray(response)) {
        allOrders = response;
      } else if (response.success && Array.isArray(response.data)) {
        allOrders = response.data;
      } else if (response.data && Array.isArray(response.data)) {
        allOrders = response.data;
      } else {
        console.error('❌ خطأ في تنسيق البيانات:', response);
        allOrders = [];
      }

      // Cache the data for all filters since we got all orders
      setOrdersCache(prev => ({
        ...prev,
        pending: allOrders,
        preparing: allOrders,
        ready: allOrders,
        all: allOrders
      }));
      
      setLastFetchTime(prev => ({
        ...prev,
        pending: now,
        preparing: now,
        ready: now,
        all: now
      }));

      setOrders(allOrders);
      console.log(`✅ تم تحميل ${allOrders.length} طلب للفلتر: ${filter}`);
      
    } catch (error) {
      console.error(`❌ خطأ في تحميل بيانات الفلتر ${filter}:`, error);
      showError('فشل في جلب الطلبات');
      setOrders([]);
    } finally {
      setLoadingStates(prev => ({ ...prev, [filter]: false }));
    }
  }, [ordersCache, lastFetchTime, showError]);

  // تصفية الطلبات حسب النوع والطباخ
  const getFilteredOrders = useCallback(() => {
    let filtered = orders;

    // تصفية حسب النوع والطباخ
    if (currentFilter === 'pending') {
      // الطلبات المعلقة - يمكن لأي طباخ قبولها
      filtered = orders.filter(order => order.status === 'pending');
    } else if (currentFilter === 'preparing') {
      // الطلبات قيد التحضير - عرض طلبات هذا الطباخ فقط
      filtered = orders.filter(order => 
        order.status === 'preparing' && 
        (order.chefName === chefName || order.chefName === user?.username)
      );
    } else if (currentFilter === 'ready') {
      // الطلبات الجاهزة - عرض طلبات هذا الطباخ فقط
      filtered = orders.filter(order =>
        (order.status === 'ready' || order.status === 'completed') &&
        (order.chefName === chefName || order.chefName === user?.username)
      );
    } else if (currentFilter === 'all') {
      // جميع الطلبات - عرض طلبات هذا الطباخ + الطلبات المعلقة
      filtered = orders.filter(order => {
        // عرض الطلبات المعلقة (يمكن لأي طباخ قبولها)
        if (order.status === 'pending') return true;

        // عرض طلبات هذا الطباخ فقط للحالات الأخرى
        return (order.chefName === chefName || order.chefName === user?.username);
      });
    }

    return filtered.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
  }, [orders, currentFilter, chefName, user?.username]);

  // الحصول على الطلبات مع pagination
  const getPaginatedOrders = useCallback(() => {
    const filteredOrders = getFilteredOrders();
    
    if (showAllOrders) {
      return filteredOrders;
    }
    
    const startIndex = (currentPage - 1) * ordersPerPage;
    const endIndex = startIndex + ordersPerPage;
    return filteredOrders.slice(startIndex, endIndex);
  }, [getFilteredOrders, showAllOrders, currentPage, ordersPerPage]);

  // Change filter with smart loading
  const changeFilter = useCallback(async (newFilter: FilterType) => {
    if (newFilter === currentFilter) return;
    
    console.log(`🔄 تغيير الفلتر من ${currentFilter} إلى ${newFilter}`);
    setCurrentFilter(newFilter);
    setCurrentPage(1); // إعادة تعيين الصفحة الحالية
    
    // إغلاق القائمة الجانبية تلقائياً (خاصة في الهاتف)
    setSidebarOpen(false);
    
    // Load data for the new filter
    await loadFilterData(newFilter);
  }, [currentFilter, loadFilterData]);

  // جلب الطلبات (محدثة للتوافق مع النظام الجديد)
  const fetchOrders = useCallback(async () => {
    await loadFilterData(currentFilter);
  }, [currentFilter, loadFilterData]);  // قبول طلب (تغيير الحالة إلى preparing)
  const acceptOrder = async (orderId: string) => {
    try {
      console.log('🔄 محاولة قبول الطلب:', { orderId, chefName, chefId });
      
      // التحقق من وجود token
      const token = localStorage.getItem('token') || localStorage.getItem('authToken');
      if (!token) {
        showError('جلسة المصادقة منتهية الصلاحية. يرجى تسجيل الدخول مرة أخرى.');
        if (onLogout) onLogout();
        return;
      }      
      const response = await authenticatedPut(`/api/v1/orders/${orderId}`, {
        status: 'preparing',
        chefName: chefName,
        chefId: chefId
      });

      console.log('📊 استجابة قبول الطلب:', response);      if (response && (response.success || response.status === 'success')) {
        showSuccess('تم قبول الطلب بنجاح');
        
        // Clear cache and reload current filter data
        setOrdersCache({});
        setLastFetchTime({});
        loadFilterData(currentFilter);// إرسال إشعار عبر Socket.IO للنادل والمدير
        socket.emit('order-status-update', {
          orderId,
          newStatus: 'preparing',
          oldStatus: 'pending',
          chefName,
          chefId,
          message: `تم قبول الطلب من قبل ${chefName}`,
          timestamp: new Date().toISOString(),
          tableNumber: response.order?.tableNumber || 'غير محدد'
        });

        // إشعار خاص للمدير
        socket.emit('chef-activity', {
          action: 'order-accepted',
          chefName,
          orderId,
          timestamp: new Date().toISOString()
        });
      } else {
        console.error('❌ فشل في قبول الطلب:', response);
        showError(response?.message || 'فشل في قبول الطلب');
      }
    } catch (error: any) {
      console.error('❌ خطأ في قبول الطلب:', error);
      
      if (error.message?.includes('Authentication required')) {
        showError('انتهت جلسة المصادقة. يرجى تسجيل الدخول مرة أخرى.');
        if (onLogout) onLogout();
      } else {
        showError('فشل في قبول الطلب');
      }
    }
  };  // إنهاء طلب (تغيير الحالة إلى ready)
  const completeOrder = async (orderId: string) => {
    try {
      console.log('🔄 محاولة إنهاء الطلب:', { orderId, chefName });
      
      // التحقق من وجود token
      const token = localStorage.getItem('token') || localStorage.getItem('authToken');
      if (!token) {
        showError('جلسة المصادقة منتهية الصلاحية. يرجى تسجيل الدخول مرة أخرى.');
        if (onLogout) onLogout();
        return;
      }      
      const response = await authenticatedPut(`/api/v1/orders/${orderId}`, {
        status: 'ready',
        chefName: chefName
      });

      console.log('📊 استجابة إنهاء الطلب:', response);      if (response && (response.success || response.status === 'success')) {
        showSuccess('تم إنهاء الطلب بنجاح');
        
        // Clear cache and reload current filter data
        setOrdersCache({});
        setLastFetchTime({});
        loadFilterData(currentFilter);// إرسال إشعار عبر Socket.IO للنادل والمدير
        socket.emit('order-status-update', {
          orderId,
          newStatus: 'ready',
          oldStatus: 'preparing', 
          chefName,
          chefId,
          message: `تم إنهاء تحضير الطلب من قبل ${chefName}`,
          timestamp: new Date().toISOString(),
          tableNumber: response.order?.tableNumber || 'غير محدد'
        });

        // إشعار خاص للنادل أن الطلب جاهز
        socket.emit('order-ready-notification', {
          orderId,
          orderNumber: response.order?.orderNumber,
          tableNumber: response.order?.tableNumber,
          chefName,
          message: `طلبك جاهز للتسليم!`,
          timestamp: new Date().toISOString()
        });

        // إشعار للمدير عن نشاط الطباخ  
        socket.emit('chef-activity', {
          action: 'order-completed',
          chefName,
          orderId,
          timestamp: new Date().toISOString()
        });
      } else {
        console.error('❌ فشل في إنهاء الطلب:', response);
        showError(response?.message || 'فشل في إنهاء الطلب');
      }
    } catch (error: any) {
      console.error('❌ خطأ في إنهاء الطلب:', error);
      
      if (error.message?.includes('Authentication required')) {
        showError('انتهت جلسة المصادقة. يرجى تسجيل الدخول مرة أخرى.');
        if (onLogout) onLogout();
      } else {
        showError('فشل في إنهاء الطلب');
      }
    }
  };

  // تسجيل الخروج
  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('username');
    localStorage.removeItem('userId');
    if (onLogout) {
      onLogout();
    } else {
      window.location.href = '/';
    }
  };

  // عرض تفاصيل الطلب
  const showOrderDetails = (order: Order) => {
    setSelectedOrder(order);
    setShowOrderModal(true);
  };
  // إعداد Socket.IO
  useEffect(() => {
    // تهيئة خدمة الإشعارات للصفحات غير النشطة
    const initializeNotifications = async () => {
      try {
        console.log('🔔 تهيئة خدمة الإشعارات للطباخ...');
        const success = await backgroundNotificationService.initialize();
        
        if (success) {
          console.log('✅ تم تهيئة خدمة الإشعارات للطباخ بنجاح');
          
          // اختبار الخدمة
          setTimeout(() => {
            backgroundNotificationService.sendNotification({
              title: 'مطبخ القهوة جاهز',
              body: 'تم تفعيل الإشعارات للصفحات غير النشطة',
              icon: '/coffee-logo.svg',
              tag: 'chef-system-ready',
              sound: true
            });
          }, 2000);
        }
      } catch (error) {
        console.error('❌ خطأ في تهيئة خدمة الإشعارات للطباخ:', error);
      }
    };

    initializeNotifications();

    // Load initial data for the current filter
    loadFilterData(currentFilter);

    // تسجيل الطباخ في Socket.IO
    socket.emit('register-user', {
      userId: chefId,
      role: 'chef',
      name: chefName
    });

    socket.on('registration-confirmed', (data) => {
      console.log('✅ تم تسجيل الطباخ في Socket.IO:', data);
      showInfo('🔗 متصل للتحديث الفوري');
    });

    // استقبال الطلبات الجديدة
    socket.on('new-order-notification', (data) => {
      console.log('🔔 طلب جديد وصل للطباخ:', data);
      
      // تشغيل صوت الإشعار المحسن
      notificationSound.playNotificationAdvanced({
        volume: 0.8,
        repeat: 2,
        urgent: true
      });
      
      // إشعار للصفحات غير النشطة
      backgroundNotificationService.sendNotification({
        title: '🍳 طلب جديد في المطبخ!',
        body: `طلب رقم ${data.orderNumber || 'غير محدد'} من الطاولة ${data.tableNumber || 'غير محدد'}`,
        icon: '/coffee-logo.svg',
        tag: 'new-order-chef',
        urgent: true,
        sound: true,
        data: {
          action: 'navigate_to_orders',
          orderNumber: data.orderNumber,
          tableNumber: data.tableNumber
        }
      });
      
      showSuccess(`🔔 طلب جديد رقم ${data.orderNumber || 'غير محدد'} من الطاولة ${data.tableNumber || 'غير محدد'}`);
      
      // Clear cache and reload current filter data
      setOrdersCache({});
      setLastFetchTime({});
      loadFilterData(currentFilter);
      
      // تحديث عداد الطلبات المعلقة
      setPendingOrdersCount(prev => prev + 1);
    });

    // استقبال تحديثات حالة الطلبات من طباخين آخرين
    socket.on('order-status-update', (data) => {
      console.log('🔄 تحديث حالة طلب من طباخ آخر:', data);
      
      if (data.chefName !== chefName) {
        showInfo(`${data.chefName}: ${data.message}`);
      }
      
      // Clear cache and reload current filter data
      setOrdersCache({});
      setLastFetchTime({});
      loadFilterData(currentFilter);
      
      // تحديث العداد حسب الحالة
      if (data.newStatus === 'preparing' && data.oldStatus === 'pending') {
        setPendingOrdersCount(prev => Math.max(0, prev - 1));
      }
    });

    socket.on('connect', () => {
      console.log('🔌 الطباخ متصل بـ Socket.IO');
      setIsSocketConnected(true);      socket.emit('register-user', {
        userId: chefId,
        role: 'chef',
        name: chefName
      });
    });

    socket.on('disconnect', () => {
      console.log('❌ انقطع اتصال الطباخ مع Socket.IO');
      setIsSocketConnected(false);
      
      // إشعار انقطاع الاتصال للطباخ
      backgroundNotificationService.sendNotification({
        title: '⚠️ انقطع الاتصال',
        body: 'تم فقدان الاتصال مع الخادم - قد لا تصل طلبات جديدة',
        icon: '/coffee-logo.svg',
        tag: 'chef-connection-lost',
        urgent: true,
        sound: true
      });
    });

    socket.on('connect_error', (error) => {
      console.error('❌ خطأ في اتصال Socket.IO للطباخ:', error);
      setIsSocketConnected(false);
      
      // إشعار خطأ الاتصال
      backgroundNotificationService.sendNotification({
        title: '❌ خطأ في الاتصال',
        body: 'حدث خطأ في الاتصال مع الخادم',
        icon: '/coffee-logo.svg',
        tag: 'chef-connection-error',
        urgent: false,
        sound: false
      });
    });

    return () => {
      socket.off('registration-confirmed');
      socket.off('new-order-notification');
      socket.off('order-status-update');
      socket.off('connect');
      socket.off('disconnect');
      socket.off('connect_error');
    };
  }, [chefId, chefName, showSuccess, showInfo, currentFilter, loadFilterData]);

  // الطلبات مع التصفية والـ pagination
  const filteredOrders = getFilteredOrders();
  const paginatedOrders = getPaginatedOrders();

  // حساب الإحصائيات - عرض جميع الطلبات
  const stats = {
    pending: orders.filter(o => o.status === 'pending').length,
    preparing: orders.filter(o => o.status === 'preparing').length,
    ready: orders.filter(o => o.status === 'ready' || o.status === 'completed').length,
    all: orders.length
  };

  return (
    <div className="chef-dashboard">
      {/* Header */}
      <header className="chef-header">
        <div className="header-content">
          <div className="header-left">
            <button
              className="sidebar-toggle"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              ☰
            </button>
            <h1>لوحة الطباخ</h1>
          </div>
          <div className="header-right">
            <span className="chef-name">مرحباً، {chefName}</span>
            <button className="logout-btn" onClick={handleLogout}>
              تسجيل الخروج
            </button>
          </div>
        </div>
      </header>

      <div className="dashboard-content">
        {/* Sidebar */}
        <aside className={`chef-sidebar ${sidebarOpen ? 'open' : ''}`}>
          <div className="sidebar-content">
            <div className="chef-profile">
              <div className="chef-avatar">👨‍🍳</div>
              <h3>{chefName}</h3>
              <p>طباخ</p>
            </div>            <nav className="filter-nav">
              <button
                className={`filter-btn ${currentFilter === 'pending' ? 'active' : ''}`}
                onClick={() => changeFilter('pending')}
                disabled={loadingStates.pending}
              >
                <span className="filter-icon">⏳</span>
                <span className="filter-text">
                  {loadingStates.pending ? 'جاري التحميل...' : 'قيد الانتظار'}
                </span>
                <span className="filter-count">{stats.pending}</span>
              </button>

              <button
                className={`filter-btn ${currentFilter === 'preparing' ? 'active' : ''}`}
                onClick={() => changeFilter('preparing')}
                disabled={loadingStates.preparing}
              >
                <span className="filter-icon">👨‍🍳</span>
                <span className="filter-text">
                  {loadingStates.preparing ? 'جاري التحميل...' : 'قيد التحضير'}
                </span>
                <span className="filter-count">{stats.preparing}</span>
              </button>

              <button
                className={`filter-btn ${currentFilter === 'ready' ? 'active' : ''}`}
                onClick={() => changeFilter('ready')}
                disabled={loadingStates.ready}
              >
                <span className="filter-icon">✅</span>
                <span className="filter-text">
                  {loadingStates.ready ? 'جاري التحميل...' : 'جاهزة'}
                </span>
                <span className="filter-count">{stats.ready}</span>
              </button>

              <button
                className={`filter-btn ${currentFilter === 'all' ? 'active' : ''}`}
                onClick={() => changeFilter('all')}
                disabled={loadingStates.all}
              >
                <span className="filter-icon">📋</span>
                <span className="filter-text">
                  {loadingStates.all ? 'جاري التحميل...' : 'الكل'}
                </span>
                <span className="filter-count">{stats.all}</span>
              </button>
            </nav>
          </div>
        </aside>

        {/* Overlay for mobile when sidebar is open */}
        {sidebarOpen && (
          <div 
            className="sidebar-overlay" 
            onClick={() => setSidebarOpen(false)}
          ></div>
        )}

        {/* Main Content */}
        <main className="chef-main">
          <div className="orders-section">
            <div className="section-header">
              <h2>
                {currentFilter === 'pending' && 'الطلبات قيد الانتظار'}
                {currentFilter === 'preparing' && 'الطلبات قيد التحضير'}
                {currentFilter === 'ready' && 'الطلبات الجاهزة'}
                {currentFilter === 'all' && 'جميع الطلبات'}
              </h2>
              <span className="orders-count">{filteredOrders.length} طلب</span>
            </div>            {(loadingStates[currentFilter] || loading) ? (
              <div className="loading">
                <div className="loading-spinner"></div>
                <p>جاري تحميل {
                  currentFilter === 'pending' ? 'الطلبات قيد الانتظار' :
                  currentFilter === 'preparing' ? 'الطلبات قيد التحضير' :
                  currentFilter === 'ready' ? 'الطلبات الجاهزة' :
                  'جميع الطلبات'
                }...</p>
              </div>
            ) : filteredOrders.length === 0 ? (
              <div className="no-orders">
                <div className="no-orders-icon">📋</div>
                <p>لا توجد طلبات في هذا القسم</p>
              </div>
            ) : (
              <div className="orders-grid">
                {paginatedOrders.map(order => (
                  <div key={order._id} className={`order-card ${order.status}`}>
                    <div className="order-header">
                      <div className="order-number">#{order.orderNumber}</div>
                      <div className={`order-status ${order.status}`}>
                        {order.status === 'pending' && 'قيد الانتظار'}
                        {order.status === 'preparing' && 'قيد التحضير'}
                        {order.status === 'ready' && 'جاهز'}
                        {order.status === 'completed' && 'مكتمل'}
                      </div>
                    </div>

                    <div className="order-info">
                      {order.tableNumber && (
                        <div className="order-table">
                          <span className="icon">🏓</span>
                          طاولة {order.tableNumber}
                        </div>
                      )}
                      {order.customerName && (
                        <div className="order-customer">
                          <span className="icon">👤</span>
                          {order.customerName}
                        </div>
                      )}
                      {order.waiterName && (
                        <div className="order-waiter">
                          <span className="icon">🍽️</span>
                          {order.waiterName}
                        </div>
                      )}
                    </div>

                    <div className="order-items">
                      <div className="items-count">
                        {order.items.length} صنف - {order.totalAmount} جنيه
                      </div>                      <div className="items-preview">
                        {order.items.slice(0, 2).map((item, index) => {
                          console.log('🔍 عرض عنصر في القائمة الرئيسية:', {
                            itemName: item.name,
                            notes: item.notes,
                            specialRequests: item.specialRequests,
                            hasNotes: !!(item.specialRequests || item.notes)
                          });
                          
                          return (
                            <div key={index} className="item-preview">
                              <span className="item-name">
                                {item.name} ({item.quantity})
                              </span>
                              {(item.specialRequests || item.notes) && (
                                <div className="item-special-note">
                                  📝 {item.specialRequests || item.notes}
                                </div>
                              )}
                            </div>
                          );
                        })}
                        {order.items.length > 2 && (
                          <span className="more-items">
                            +{order.items.length - 2} المزيد
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="order-time">
                      <span className="icon">⏰</span>
                      {new Date(order.createdAt).toLocaleTimeString('ar-SA', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>

                    <div className="order-actions">                      <button
                        className="details-btn"
                        onClick={() => showOrderDetails(order)}
                      >
                        <i className="fas fa-eye"></i>
                        التفاصيل
                      </button>{order.status === 'pending' && (
                        <button
                          className="accept-btn"
                          onClick={() => {
                            console.log('🔘 تم الضغط على زر قبول الطلب:', order._id);
                            acceptOrder(order._id);
                          }}
                        >
                          <i className="fas fa-check"></i>
                          قبول الطلب
                        </button>
                      )}                      {order.status === 'preparing' && (
                        <button
                          className="complete-btn"
                          onClick={() => {
                            console.log('🔘 تم الضغط على زر إنهاء التحضير:', order._id);
                            completeOrder(order._id);
                          }}
                        >
                          <i className="fas fa-check-circle"></i>
                          إنهاء التحضير
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {/* Pagination Controls */}
            {filteredOrders.length > 0 && (
              <div className="pagination-controls">
                <div className="pagination-info">
                  <span className="total-orders-count">
                    <i className="fas fa-receipt"></i>
                    إجمالي الطلبات: <strong>{filteredOrders.length}</strong>
                  </span>
                  <div className="view-options">
                    <button 
                      className={`view-option-btn ${showAllOrders ? 'active' : ''}`}
                      onClick={() => setShowAllOrders(!showAllOrders)}
                    >
                      <i className="fas fa-list"></i>
                      {showAllOrders ? 'عرض بصفحات' : 'عرض الكل'}
                    </button>
                  </div>
                </div>
                
                {!showAllOrders && filteredOrders.length > ordersPerPage && (
                  <div className="pagination-controls-main">
                    <div className="pagination-size">
                      <label>عدد الطلبات في الصفحة:</label>
                      <select 
                        value={ordersPerPage} 
                        onChange={(e) => {
                          setOrdersPerPage(Number(e.target.value));
                          setCurrentPage(1);
                        }}
                        className="per-page-select"
                        aria-label="عدد الطلبات في الصفحة"
                      >
                        <option value={5}>5</option>
                        <option value={10}>10</option>
                        <option value={15}>15</option>
                        <option value={20}>20</option>
                      </select>
                    </div>
                    
                    <div className="pagination-buttons">
                      {(() => {
                        const totalOrders = filteredOrders.length;
                        const totalPages = Math.ceil(totalOrders / ordersPerPage);
                        
                        return (
                          <div className="page-navigation">
                            <button
                              className="page-btn prev-btn"
                              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                              disabled={currentPage === 1}
                            >
                              <i className="fas fa-chevron-right"></i>
                              السابق
                            </button>
                            
                            <div className="page-numbers">
                              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                let pageNum;
                                if (totalPages <= 5) {
                                  pageNum = i + 1;
                                } else if (currentPage <= 3) {
                                  pageNum = i + 1;
                                } else if (currentPage >= totalPages - 2) {
                                  pageNum = totalPages - 4 + i;
                                } else {
                                  pageNum = currentPage - 2 + i;
                                }
                                
                                return (
                                  <button
                                    key={pageNum}
                                    className={`page-number ${currentPage === pageNum ? 'active' : ''}`}
                                    onClick={() => setCurrentPage(pageNum)}
                                  >
                                    {pageNum}
                                  </button>
                                );
                              })}
                            </div>
                            
                            <button
                              className="page-btn next-btn"
                              onClick={() => setCurrentPage(Math.min(Math.ceil(totalOrders / ordersPerPage), currentPage + 1))}
                              disabled={currentPage >= Math.ceil(totalOrders / ordersPerPage)}
                            >
                              التالي
                              <i className="fas fa-chevron-left"></i>
                            </button>
                            
                            <div className="page-info">
                              صفحة {currentPage} من {Math.ceil(totalOrders / ordersPerPage)}
                            </div>
                          </div>
                        );
                      })()}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </main>
      </div>

      {/* Order Details Modal */}
      {showOrderModal && selectedOrder && (
        <div className="modal-overlay" onClick={() => setShowOrderModal(false)}>
          <div className="modal-content" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h3>تفاصيل الطلب #{selectedOrder.orderNumber}</h3>
              <button
                className="close-btn"
                onClick={() => setShowOrderModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <div className="order-details">
                <div className="detail-row">
                  <span className="label">الحالة:</span>
                  <span className={`value status ${selectedOrder.status}`}>
                    {selectedOrder.status === 'pending' && 'قيد الانتظار'}
                    {selectedOrder.status === 'preparing' && 'قيد التحضير'}
                    {selectedOrder.status === 'ready' && 'جاهز'}
                    {selectedOrder.status === 'completed' && 'مكتمل'}
                  </span>
                </div>

                {selectedOrder.tableNumber && (
                  <div className="detail-row">
                    <span className="label">الطاولة:</span>
                    <span className="value">{selectedOrder.tableNumber}</span>
                  </div>
                )}

                {selectedOrder.customerName && (
                  <div className="detail-row">
                    <span className="label">العميل:</span>
                    <span className="value">{selectedOrder.customerName}</span>
                  </div>
                )}

                {selectedOrder.waiterName && (
                  <div className="detail-row">
                    <span className="label">النادل:</span>
                    <span className="value">{selectedOrder.waiterName}</span>
                  </div>
                )}

                {selectedOrder.chefName && (
                  <div className="detail-row">
                    <span className="label">الطباخ:</span>
                    <span className="value">{selectedOrder.chefName}</span>
                  </div>
                )}

                <div className="detail-row">
                  <span className="label">الوقت:</span>
                  <span className="value">
                    {new Date(selectedOrder.createdAt).toLocaleString('ar-SA')}
                  </span>
                </div>

                <div className="detail-row">
                  <span className="label">المجموع:</span>
                  <span className="value total">{selectedOrder.totalAmount} جنيه</span>
                </div>
              </div>

              <div className="order-items-details">
                <h4>الأصناف:</h4>
                <div className="items-list">
                  {selectedOrder.items.map((item, index) => (
                    <div key={index} className="item-detail">
                      <div className="item-info">
                        <span className="item-name">{item.name}</span>
                        <span className="item-quantity">× {item.quantity}</span>
                      </div>
                      <div className="item-price">{item.price * item.quantity} جنيه</div>                      {(item.notes || item.specialRequests) && (
                        <div className="item-notes">
                          {item.specialRequests && (
                            <div>متطلبات خاصة: {item.specialRequests}</div>
                          )}
                          {item.notes && item.notes !== item.specialRequests && (
                            <div>ملاحظة: {item.notes}</div>
                          )}
                          {item.modifications && item.modifications.length > 0 && (
                            <div>تعديلات: {item.modifications.join(', ')}</div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {selectedOrder.notes && (
                <div className="order-notes">
                  <h4>ملاحظات الطلب:</h4>
                  <p>{selectedOrder.notes}</p>
                </div>
              )}
            </div>

            <div className="modal-actions">              {selectedOrder.status === 'pending' && (
                <button
                  className="accept-btn"
                  onClick={() => {
                    console.log('🔘 تم الضغط على زر قبول الطلب من النافذة المنبثقة:', selectedOrder._id);
                    acceptOrder(selectedOrder._id);
                    setShowOrderModal(false);
                  }}
                >
                  <i className="fas fa-check"></i>
                  قبول الطلب
                </button>
              )}              {selectedOrder.status === 'preparing' && (
                <button
                  className="complete-btn"
                  onClick={() => {
                    console.log('🔘 تم الضغط على زر إنهاء التحضير من النافذة المنبثقة:', selectedOrder._id);
                    completeOrder(selectedOrder._id);
                    setShowOrderModal(false);
                  }}
                >
                  <i className="fas fa-check-circle"></i>
                  إنهاء التحضير
                </button>
              )}              <button
                className="close-modal-btn"
                onClick={() => setShowOrderModal(false)}
              >
                <i className="fas fa-times"></i>
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}