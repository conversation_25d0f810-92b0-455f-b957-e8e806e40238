/* =================================== */
/* Chef Variables - متغيرات الطباخ */
/* =================================== */

:root {
  /* =================================== */
  /* ألوان العلامة التجارية للطباخ */
  /* =================================== */
  
  --chef-primary-color: #2c3e50;
  --chef-secondary-color: #3498db;
  --chef-accent-color: #e74c3c;
  --chef-success-color: #27ae60;
  --chef-warning-color: #f39c12;
  --chef-info-color: #17a2b8;
  --chef-danger-color: #dc3545;
  --chef-light-color: #f8f9fa;
  --chef-dark-color: #343a40;
  
  /* =================================== */
  /* ألوان حالات الطلبات */
  /* =================================== */
  
  --chef-status-pending: #f39c12;
  --chef-status-pending-bg: #fff3cd;
  --chef-status-pending-border: #ffeaa7;
  
  --chef-status-preparing: #17a2b8;
  --chef-status-preparing-bg: #d1ecf1;
  --chef-status-preparing-border: #bee5eb;
  
  --chef-status-ready: #28a745;
  --chef-status-ready-bg: #d4edda;
  --chef-status-ready-border: #c3e6cb;
  
  --chef-status-completed: #6c757d;
  --chef-status-completed-bg: #e2e3e5;
  --chef-status-completed-border: #d6d8db;
  
  --chef-status-cancelled: #dc3545;
  --chef-status-cancelled-bg: #f8d7da;
  --chef-status-cancelled-border: #f5c6cb;
  
  /* =================================== */
  /* ألوان الخلفيات */
  /* =================================== */
  
  --chef-bg-primary: #ffffff;
  --chef-bg-secondary: #f8f9fa;
  --chef-bg-tertiary: #e9ecef;
  --chef-bg-quaternary: #dee2e6;
  
  --chef-bg-sidebar: #ffffff;
  --chef-bg-header: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
  --chef-bg-card: #ffffff;
  --chef-bg-card-hover: #f8f9fa;
  
  --chef-bg-overlay: rgba(0, 0, 0, 0.5);
  --chef-bg-modal: rgba(0, 0, 0, 0.8);
  
  /* =================================== */
  /* ألوان النصوص */
  /* =================================== */
  
  --chef-text-primary: #2c3e50;
  --chef-text-secondary: #6c757d;
  --chef-text-muted: #adb5bd;
  --chef-text-light: #ffffff;
  --chef-text-dark: #212529;
  
  --chef-text-success: #155724;
  --chef-text-warning: #856404;
  --chef-text-danger: #721c24;
  --chef-text-info: #0c5460;
  
  /* =================================== */
  /* الحدود والخطوط */
  /* =================================== */
  
  --chef-border-color: #dee2e6;
  --chef-border-light: #e9ecef;
  --chef-border-medium: #ced4da;
  --chef-border-dark: #adb5bd;
  
  --chef-border-width: 1px;
  --chef-border-width-thick: 2px;
  --chef-border-width-thin: 0.5px;
  
  --chef-border-style: solid;
  
  /* =================================== */
  /* نصف القطر */
  /* =================================== */
  
  --chef-border-radius: 12px;
  --chef-border-radius-sm: 6px;
  --chef-border-radius-md: 8px;
  --chef-border-radius-lg: 16px;
  --chef-border-radius-xl: 20px;
  --chef-border-radius-round: 50%;
  
  /* =================================== */
  /* الظلال */
  /* =================================== */
  
  --chef-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  --chef-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --chef-shadow-md: 0 4px 15px rgba(0, 0, 0, 0.1);
  --chef-shadow-lg: 0 6px 20px rgba(0, 0, 0, 0.15);
  --chef-shadow-xl: 0 8px 25px rgba(0, 0, 0, 0.2);
  
  --chef-shadow-hover: 0 6px 20px rgba(0, 0, 0, 0.15);
  --chef-shadow-focus: 0 0 0 3px rgba(52, 152, 219, 0.25);
  
  --chef-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  
  /* =================================== */
  /* المسافات */
  /* =================================== */
  
  --chef-spacing-xs: 0.25rem;    /* 4px */
  --chef-spacing-sm: 0.5rem;     /* 8px */
  --chef-spacing-md: 1rem;       /* 16px */
  --chef-spacing-lg: 1.5rem;     /* 24px */
  --chef-spacing-xl: 2rem;       /* 32px */
  --chef-spacing-xxl: 3rem;      /* 48px */
  --chef-spacing-xxxl: 4rem;     /* 64px */
  
  /* =================================== */
  /* أحجام الخطوط */
  /* =================================== */
  
  --chef-font-size-xs: 0.75rem;    /* 12px */
  --chef-font-size-sm: 0.875rem;   /* 14px */
  --chef-font-size-md: 1rem;       /* 16px */
  --chef-font-size-lg: 1.125rem;   /* 18px */
  --chef-font-size-xl: 1.25rem;    /* 20px */
  --chef-font-size-xxl: 1.5rem;    /* 24px */
  --chef-font-size-xxxl: 2rem;     /* 32px */
  --chef-font-size-display: 2.5rem; /* 40px */
  
  /* =================================== */
  /* أوزان الخطوط */
  /* =================================== */
  
  --chef-font-weight-light: 300;
  --chef-font-weight-normal: 400;
  --chef-font-weight-medium: 500;
  --chef-font-weight-semibold: 600;
  --chef-font-weight-bold: 700;
  --chef-font-weight-extrabold: 800;
  --chef-font-weight-black: 900;
  
  /* =================================== */
  /* ارتفاعات الأسطر */
  /* =================================== */
  
  --chef-line-height-tight: 1.2;
  --chef-line-height-normal: 1.5;
  --chef-line-height-relaxed: 1.75;
  --chef-line-height-loose: 2;
  
  /* =================================== */
  /* الانتقالات */
  /* =================================== */
  
  --chef-transition-fast: 0.15s ease;
  --chef-transition-normal: 0.3s ease;
  --chef-transition-slow: 0.5s ease;
  
  --chef-transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --chef-transition-smooth: 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  
  /* =================================== */
  /* الأبعاد */
  /* =================================== */
  
  --chef-sidebar-width: 320px;
  --chef-sidebar-width-collapsed: 80px;
  --chef-header-height: 80px;
  --chef-footer-height: 60px;
  
  --chef-card-min-height: 200px;
  --chef-card-max-width: 400px;
  
  --chef-button-height: 40px;
  --chef-button-height-sm: 32px;
  --chef-button-height-lg: 48px;
  
  --chef-input-height: 40px;
  --chef-input-height-sm: 32px;
  --chef-input-height-lg: 48px;
  
  /* =================================== */
  /* نقاط التوقف للاستجابة */
  /* =================================== */
  
  --chef-breakpoint-xs: 0;
  --chef-breakpoint-sm: 576px;
  --chef-breakpoint-md: 768px;
  --chef-breakpoint-lg: 992px;
  --chef-breakpoint-xl: 1200px;
  --chef-breakpoint-xxl: 1400px;
  
  /* =================================== */
  /* Z-Index */
  /* =================================== */
  
  --chef-z-dropdown: 1000;
  --chef-z-sticky: 1020;
  --chef-z-fixed: 1030;
  --chef-z-modal-backdrop: 1040;
  --chef-z-modal: 1050;
  --chef-z-popover: 1060;
  --chef-z-tooltip: 1070;
  --chef-z-toast: 1080;
  
  /* =================================== */
  /* متغيرات خاصة بالطباخ */
  /* =================================== */
  
  --chef-order-card-width: 380px;
  --chef-order-card-min-height: 250px;
  --chef-order-grid-gap: 1.5rem;
  
  --chef-filter-btn-height: 50px;
  --chef-profile-avatar-size: 80px;
  
  --chef-notification-duration: 5000ms;
  --chef-refresh-interval: 30000ms;
  
  /* =================================== */
  /* ألوان الحالة التفاعلية */
  /* =================================== */
  
  --chef-hover-opacity: 0.8;
  --chef-active-opacity: 0.9;
  --chef-disabled-opacity: 0.6;
  
  --chef-hover-transform: translateY(-2px);
  --chef-active-transform: translateY(0);
  
  /* =================================== */
  /* متغيرات الأنيميشن */
  /* =================================== */
  
  --chef-pulse-duration: 2s;
  --chef-shake-duration: 0.5s;
  --chef-fade-duration: 0.3s;
  --chef-slide-duration: 0.3s;
  
  /* =================================== */
  /* متغيرات الطباعة */
  /* =================================== */
  
  --chef-print-font-size: 12pt;
  --chef-print-line-height: 1.4;
  --chef-print-margin: 1cm;
  
  /* =================================== */
  /* متغيرات إمكانية الوصول */
  /* =================================== */
  
  --chef-focus-outline-width: 2px;
  --chef-focus-outline-offset: 2px;
  --chef-focus-outline-color: var(--chef-secondary-color);
  
  --chef-min-touch-target: 44px;
  --chef-min-click-target: 24px;
}

/* =================================== */
/* متغيرات الوضع المظلم */
/* =================================== */

@media (prefers-color-scheme: dark) {
  :root {
    --chef-bg-primary: #1a1a1a;
    --chef-bg-secondary: #2d2d2d;
    --chef-bg-tertiary: #404040;
    --chef-bg-quaternary: #555555;
    
    --chef-bg-sidebar: #2d2d2d;
    --chef-bg-card: #2d2d2d;
    --chef-bg-card-hover: #404040;
    
    --chef-text-primary: #ffffff;
    --chef-text-secondary: #cccccc;
    --chef-text-muted: #999999;
    --chef-text-dark: #ffffff;
    
    --chef-border-color: #404040;
    --chef-border-light: #555555;
    --chef-border-medium: #666666;
    --chef-border-dark: #777777;
    
    --chef-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    --chef-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --chef-shadow-md: 0 4px 15px rgba(0, 0, 0, 0.3);
    --chef-shadow-lg: 0 6px 20px rgba(0, 0, 0, 0.4);
    --chef-shadow-xl: 0 8px 25px rgba(0, 0, 0, 0.5);
  }
}

/* =================================== */
/* متغيرات التباين العالي */
/* =================================== */

@media (prefers-contrast: high) {
  :root {
    --chef-border-color: #000000;
    --chef-text-secondary: #000000;
    --chef-shadow: none;
    --chef-shadow-sm: none;
    --chef-shadow-md: 0 0 0 2px #000000;
    --chef-shadow-lg: 0 0 0 3px #000000;
  }
}

/* =================================== */
/* متغيرات الحركة المقللة */
/* =================================== */

@media (prefers-reduced-motion: reduce) {
  :root {
    --chef-transition-fast: 0.01ms;
    --chef-transition-normal: 0.01ms;
    --chef-transition-slow: 0.01ms;
    --chef-transition-bounce: 0.01ms;
    --chef-transition-smooth: 0.01ms;
    
    --chef-pulse-duration: 0.01ms;
    --chef-shake-duration: 0.01ms;
    --chef-fade-duration: 0.01ms;
    --chef-slide-duration: 0.01ms;
  }
}
