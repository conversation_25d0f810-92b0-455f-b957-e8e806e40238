/* =================================== */
/* Chef Dashboard - لوحة الطباخ */
/* =================================== */

/* متغيرات الألوان الخاصة بالطباخ */
:root {
  --chef-primary-color: #2c3e50;
  --chef-secondary-color: #3498db;
  --chef-accent-color: #e74c3c;
  --chef-success-color: #27ae60;
  --chef-warning-color: #f39c12;
  --chef-info-color: #17a2b8;
  --chef-light-color: #ecf0f1;
  --chef-dark-color: #2c3e50;
  
  --chef-bg-primary: #ffffff;
  --chef-bg-secondary: #f8f9fa;
  --chef-bg-tertiary: #e9ecef;
  
  --chef-text-primary: #2c3e50;
  --chef-text-secondary: #6c757d;
  --chef-text-light: #ffffff;
  
  --chef-border-color: #dee2e6;
  --chef-border-radius: 12px;
  --chef-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  --chef-shadow-hover: 0 6px 20px rgba(0, 0, 0, 0.15);
  
  --chef-spacing-xs: 0.25rem;
  --chef-spacing-sm: 0.5rem;
  --chef-spacing-md: 1rem;
  --chef-spacing-lg: 1.5rem;
  --chef-spacing-xl: 2rem;
}

/* Container الرئيسي */
.chef-dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, var(--chef-bg-secondary) 0%, var(--chef-bg-tertiary) 100%);
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header الطباخ */
.chef-header {
  background: linear-gradient(135deg, var(--chef-primary-color), var(--chef-secondary-color));
  color: var(--chef-text-light);
  padding: var(--chef-spacing-lg) var(--chef-spacing-xl);
  box-shadow: var(--chef-shadow);
  z-index: 1000;
  position: relative;
}

.chef-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  pointer-events: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--chef-spacing-lg);
}

.chef-title {
  font-size: 1.8rem;
  font-weight: 800;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.chef-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--chef-spacing-md);
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: var(--chef-text-light);
  padding: var(--chef-spacing-sm) var(--chef-spacing-md);
  border-radius: var(--chef-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--chef-spacing-sm);
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.logout-btn {
  background: var(--chef-accent-color);
  border: none;
  color: var(--chef-text-light);
  padding: var(--chef-spacing-sm) var(--chef-spacing-md);
  border-radius: var(--chef-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--chef-spacing-sm);
}

.logout-btn:hover {
  background: #c0392b;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

/* محتوى لوحة التحكم */
.dashboard-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* الشريط الجانبي */
.chef-sidebar {
  width: 320px;
  background: var(--chef-bg-primary);
  border-left: 1px solid var(--chef-border-color);
  box-shadow: var(--chef-shadow);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  z-index: 100;
}

.chef-sidebar.open {
  transform: translateX(0);
}

.sidebar-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--chef-spacing-lg);
}

/* ملف الطباخ */
.chef-profile {
  text-align: center;
  padding: var(--chef-spacing-lg);
  border-bottom: 2px solid var(--chef-border-color);
  margin-bottom: var(--chef-spacing-lg);
}

.chef-avatar {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--chef-primary-color), var(--chef-secondary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  margin: 0 auto var(--chef-spacing-md);
  box-shadow: var(--chef-shadow);
}

.chef-profile h3 {
  color: var(--chef-text-primary);
  margin: 0 0 var(--chef-spacing-xs) 0;
  font-size: 1.3rem;
  font-weight: 700;
}

.chef-profile p {
  color: var(--chef-text-secondary);
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

/* التنقل والفلاتر */
.filter-nav {
  display: flex;
  flex-direction: column;
  gap: var(--chef-spacing-sm);
}

.filter-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--chef-spacing-md);
  background: var(--chef-bg-secondary);
  border: 2px solid transparent;
  border-radius: var(--chef-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: right;
  font-weight: 600;
  color: var(--chef-text-primary);
}

.filter-btn:hover {
  background: var(--chef-bg-tertiary);
  border-color: var(--chef-secondary-color);
  transform: translateX(-3px);
}

.filter-btn.active {
  background: linear-gradient(135deg, var(--chef-secondary-color), var(--chef-primary-color));
  color: var(--chef-text-light);
  border-color: var(--chef-primary-color);
  box-shadow: var(--chef-shadow);
}

.filter-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.filter-btn-content {
  display: flex;
  align-items: center;
  gap: var(--chef-spacing-sm);
}

.filter-icon {
  font-size: 1.2rem;
  min-width: 24px;
  text-align: center;
}

.filter-text {
  flex: 1;
  font-size: 1rem;
}

.filter-count {
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
  padding: var(--chef-spacing-xs) var(--chef-spacing-sm);
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 700;
  min-width: 24px;
  text-align: center;
}

.filter-btn.active .filter-count {
  background: rgba(255, 255, 255, 0.3);
}

/* المحتوى الرئيسي */
.chef-main {
  flex: 1;
  padding: var(--chef-spacing-xl);
  overflow-y: auto;
  background: var(--chef-bg-secondary);
}

/* قسم الطلبات */
.orders-section {
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--chef-spacing-xl);
  padding: var(--chef-spacing-lg);
  background: var(--chef-bg-primary);
  border-radius: var(--chef-border-radius);
  box-shadow: var(--chef-shadow);
}

.section-header h2 {
  color: var(--chef-text-primary);
  margin: 0;
  font-size: 1.6rem;
  font-weight: 700;
}

.orders-count {
  background: var(--chef-secondary-color);
  color: var(--chef-text-light);
  padding: var(--chef-spacing-sm) var(--chef-spacing-md);
  border-radius: 20px;
  font-weight: 700;
  font-size: 0.95rem;
}

/* شبكة الطلبات */
.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--chef-spacing-lg);
  margin-bottom: var(--chef-spacing-xl);
}

/* بطاقة الطلب */
.order-card {
  background: var(--chef-bg-primary);
  border: 2px solid var(--chef-border-color);
  border-radius: var(--chef-border-radius);
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: var(--chef-shadow);
  position: relative;
}

.order-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--chef-shadow-hover);
  border-color: var(--chef-secondary-color);
}

/* حالات الطلبات */
.order-card.pending {
  border-left: 6px solid var(--chef-warning-color);
}

.order-card.preparing {
  border-left: 6px solid var(--chef-info-color);
}

.order-card.ready {
  border-left: 6px solid var(--chef-success-color);
}

.order-card.completed {
  border-left: 6px solid var(--chef-success-color);
  opacity: 0.8;
}

/* رأس الطلب */
.order-header {
  padding: var(--chef-spacing-lg);
  background: var(--chef-bg-secondary);
  border-bottom: 1px solid var(--chef-border-color);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.order-number {
  font-size: 1.3rem;
  font-weight: 800;
  color: var(--chef-text-primary);
  margin: 0;
}

.order-status {
  padding: var(--chef-spacing-xs) var(--chef-spacing-sm);
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.order-status.pending {
  background: var(--chef-warning-color);
  color: white;
}

.order-status.preparing {
  background: var(--chef-info-color);
  color: white;
}

.order-status.ready {
  background: var(--chef-success-color);
  color: white;
}

.order-status.completed {
  background: var(--chef-success-color);
  color: white;
}

/* معلومات الطلب */
.order-info {
  padding: var(--chef-spacing-lg);
}

.order-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--chef-spacing-md);
  margin-bottom: var(--chef-spacing-md);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--chef-spacing-xs);
}

.detail-label {
  font-size: 0.85rem;
  color: var(--chef-text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 1rem;
  color: var(--chef-text-primary);
  font-weight: 600;
}

/* عناصر الطلب */
.order-items {
  margin-top: var(--chef-spacing-md);
}

.items-header {
  font-size: 1rem;
  font-weight: 700;
  color: var(--chef-text-primary);
  margin-bottom: var(--chef-spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--chef-spacing-sm);
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: var(--chef-spacing-sm);
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--chef-spacing-sm);
  background: var(--chef-bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--chef-border-color);
}

.item-info {
  flex: 1;
}

.item-name {
  font-weight: 600;
  color: var(--chef-text-primary);
  margin-bottom: var(--chef-spacing-xs);
}

.item-notes {
  font-size: 0.85rem;
  color: var(--chef-text-secondary);
  font-style: italic;
}

.item-quantity {
  background: var(--chef-secondary-color);
  color: white;
  padding: var(--chef-spacing-xs) var(--chef-spacing-sm);
  border-radius: 20px;
  font-weight: 700;
  font-size: 0.9rem;
  min-width: 30px;
  text-align: center;
}

/* وقت الطلب */
.order-time {
  padding: var(--chef-spacing-md) var(--chef-spacing-lg);
  background: var(--chef-bg-tertiary);
  border-top: 1px solid var(--chef-border-color);
  font-size: 0.9rem;
  color: var(--chef-text-secondary);
  text-align: center;
}

/* أزرار الإجراءات */
.order-actions {
  padding: var(--chef-spacing-lg);
  background: var(--chef-bg-secondary);
  border-top: 1px solid var(--chef-border-color);
  display: flex;
  gap: var(--chef-spacing-sm);
}

.action-btn {
  flex: 1;
  padding: var(--chef-spacing-sm) var(--chef-spacing-md);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--chef-spacing-xs);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-accept {
  background: var(--chef-success-color);
  color: white;
}

.btn-accept:hover {
  background: #229954;
}

.btn-ready {
  background: var(--chef-info-color);
  color: white;
}

.btn-ready:hover {
  background: #138496;
}

.btn-details {
  background: var(--chef-secondary-color);
  color: white;
}

.btn-details:hover {
  background: #2980b9;
}

/* حالة التحميل */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--chef-spacing-xl);
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid var(--chef-border-color);
  border-top: 4px solid var(--chef-secondary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--chef-spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  color: var(--chef-text-secondary);
  font-size: 1.1rem;
  margin: 0;
}

/* حالة فارغة */
.empty-state {
  text-align: center;
  padding: var(--chef-spacing-xl);
  color: var(--chef-text-secondary);
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: var(--chef-spacing-lg);
  color: var(--chef-border-color);
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: var(--chef-spacing-sm);
  color: var(--chef-text-primary);
}

.empty-state p {
  font-size: 1.1rem;
  margin: 0;
}

/* زر تبديل الشريط الجانبي للموبايل */
.sidebar-toggle {
  display: none;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: var(--chef-text-light);
  padding: var(--chef-spacing-sm);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

/* الاستجابة للشاشات المختلفة */
@media (max-width: 1024px) {
  .chef-sidebar {
    position: fixed;
    top: 0;
    right: -320px;
    height: 100vh;
    z-index: 1001;
    transition: right 0.3s ease;
  }
  
  .chef-sidebar.open {
    right: 0;
  }
  
  .sidebar-toggle {
    display: block;
  }
  
  .orders-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .chef-header {
    padding: var(--chef-spacing-md) var(--chef-spacing-lg);
  }
  
  .chef-main {
    padding: var(--chef-spacing-lg);
  }
  
  .orders-grid {
    grid-template-columns: 1fr;
    gap: var(--chef-spacing-md);
  }
  
  .section-header {
    flex-direction: column;
    gap: var(--chef-spacing-md);
    align-items: stretch;
    text-align: center;
  }
  
  .order-actions {
    flex-direction: column;
  }
  
  .order-details {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .chef-header {
    padding: var(--chef-spacing-sm) var(--chef-spacing-md);
  }
  
  .chef-title {
    font-size: 1.5rem;
  }
  
  .chef-main {
    padding: var(--chef-spacing-md);
  }
  
  .order-card {
    border-radius: 8px;
  }
  
  .order-header,
  .order-info,
  .order-time,
  .order-actions {
    padding: var(--chef-spacing-md);
  }
  
  .chef-sidebar {
    width: 100%;
    right: -100%;
  }
  
  .sidebar-content {
    padding: var(--chef-spacing-md);
  }
}
